import { ReactNode } from 'react';

import BackActionEnum from 'configs/ConversationEnum';
import {
  AudioEntity,
  CategoryEntity,
  Character,
  DictionaryEntity,
  Entity,
  SentenceDetail,
  SentenceEntity,
} from 'types/model';
import NavigationModeEnum from '@/configs/NavigationModeEnum';

export declare type UniqueIdentifier = string | number;

export type OptionItem = Omit<Entity, 'id'> & {
  id: string | number;
};

export interface NewMemberProps {
  memberName: string;
  pin: string;
  id: number;
}

export interface MemberRegisterProps {
  memberId: number;
  onUpdate: (member: NewMemberProps) => void;
}

export interface CategoryTransformProps {
  items?: Array<any>;
  icon: string;
  id: number;
  keyx: string;
  parent_id: number;
  title: string;
  children?: Array<any>;
}

export interface IconProps {
  className?: string;
  hasRedIndicator?: boolean;
  fillColor?: string;
  strokeColor?: string;
}

export type ApproveConversationItemProps = {
  sentences: Array<SentenceEntity>;
  characters: Array<Character>;
};

export type ApproveEssayItemProps = {
  sentences: Array<SentenceEntity>;
  position: number;
};

export type ConversationItemProps = {
  onSelectSentence?: (sentence: SentenceEntity) => void;
  sentences: Array<SentenceEntity>;
  characters: Array<Character>;
  className?: string;
  isLastItem?: boolean;
  currentGroupIndex: number;
};

export type EssayItemProps = {
  onSelectSentence?: (sentence: SentenceEntity) => void;
  sentences: Array<SentenceEntity>;
  isLastItem?: boolean;
  currentPlayingSentenceIndex?: number;
};

export interface SpeakingLeftItemProps extends ConversationItemProps {
  activeCharacter?: Character;
  isPause?: boolean;
  isStartLearn?: boolean;
  setStartLearn: () => void;
}

export interface SpeakingItemRightProps extends ConversationItemProps {
  activeCharacter?: Character;
  isLastItem: boolean;
  forwardMode: NavigationModeEnum;
  isStartLearn: boolean;
  className?: string;
  setStartLearn: () => void;
}

export interface SpeakingItemProps {
  speaking: SentenceEntity;
  position: number;
  sentenceLength: number;
  className?: string | null;
  isLastRecord: boolean;
}

export type RecordSentenceProps = {
  sentence: SentenceEntity;
  isLastItem: boolean;
  isEndSpeaking: boolean;
  setScore: (score: string) => void;
  // setShowRecording: (showRecording: boolean) => void
  // setFinishSentence: (isFinish: boolean) => void
  // setShowSentence: (showSentence: boolean) => void
};

export interface PlayAudioProps {
  audios?: Array<AudioEntity> | null;
  onFinishAudio: (audio: AudioEntity) => void;
  isLeftConversation: boolean;
}

export type FinishLearnControlProps = {
  onLeanAgain: (action?: number) => void;
  isExercise?: boolean;
  setActiveTab?: React.Dispatch<React.SetStateAction<string>>;
  setFinish?: React.Dispatch<React.SetStateAction<boolean>>;
  setStart?: React.Dispatch<React.SetStateAction<boolean>>;
  handleDoNewExercise?: () => void;
};
export type ConversationControlProps = {
  onLeanAgain: (action?: number) => void;
  onFinish: () => void;
  onNextSentence: () => void;
  endSentence: boolean;
  isLastSentence: boolean;
  isFinishConversation: boolean;
  onBackSentence: () => void;
};
export type SentencePosesProps = {
  sentenceDetails: SentenceDetail[] | null;
  position: number;
  isLoadingPos: boolean;
  setPosition: (pos: number) => void;
  dictionary: DictionaryEntity | null;
};

export type SectionWelcomeProps = {
  header: ReactNode | string | null;
  children: ReactNode | null;
};
export type CategoryChildProps = {
  categories: CategoryEntity[];
  position: number;
};

export type SpeakingControlProps = {
  setIsPauseSpeak: (isPauseSpeak: boolean) => void;
  handleBackSentence: (action: BackActionEnum) => void;
  toggleRecording: () => void;
  stopMicrophone: () => void;
  showTooltipRecord: boolean;
  isPauseSpeak: boolean;
  isSendingRecord: boolean;
  isPaused: boolean;
  isStartMicrophone: boolean;
  transcript: string;
  hasCompletedAllSpeakings: boolean;
  handleFinishSpeaking: () => void;
};

export interface BaseDragItem {
  id: number;
}

export type DragItemType<T = unknown> = BaseDragItem & Partial<T>;

export type EditSentenceItem = DragItemType<{
  content: string;
  character_id: number;
  position: number;
  fullname: string;
  raw_fullname: string;
  raw_content: string;
  is_new: boolean;
  hasChange: boolean;
  voice_id: number;
  age: string;
  accent: string;
  gender: string;
  paragraph_id: number;
  document_id: number;
  course_id: number;
  image?: string;
}>;

export interface SentenceItemProps {
  sentence: EditSentenceItem;
  items: EditSentenceItem[];
  onFocusSentence: (sentence: EditSentenceItem) => void;
  setItems: (dragItems: (prevDragItems: EditSentenceItem[]) => EditSentenceItem[]) => void;
  setHasChangeSentence: (isChange: boolean) => void;
}

export type LearnProgramItem = DragItemType<{
  title: string;
  paragraph_id: number;
}>;

export interface ProgramItemProps {
  program: LearnProgramItem;
  onRemove: (program: LearnProgramItem) => void;
}

export type ModalAddCharacterProps = {
  dragItems: EditSentenceItem[];
  sentence: EditSentenceItem;
  opened: boolean;
  onOpenChange: (open: boolean) => void;
  setDragItems: (dragItems: EditSentenceItem[]) => void;
  onSuccess: (dragItems: EditSentenceItem[]) => void;
};

export type ChartItem = {
  day: string;
  GREEN?: number;
  BLUE: number;
  YELLOW?: number;
  RED?: number;
  date: number;
  isActive: boolean;
};
