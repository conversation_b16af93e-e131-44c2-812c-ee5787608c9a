'use client';

import React, { useEffect } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
// import PointsReport from '@/containers/group/report-item/PointsReport';
// import TokenReport from '@/containers/group/report-item/TokenReport';
import useReportStore from '@/store/report';
import ScrollArea from 'components/ScrollArea';
import { useTranslations } from 'next-intl';

import ExerciseReport from './ExerciseReport';
// import TokenMemberReport from './TokenMemberReport';
import LearnReport from './LearnReport';
import ListenReport from './ListenReport';
import ReadReport from './ReadReport';

// import useReportTotalLearns from '@/hooks/Ent/useReportTotalLearns';

const MemberDetailReportContainer = ({ memberId }) => {
  const t = useTranslations();
  const { params, setParams } = useReportStore();

  useEffect(() => {
    setParams({
      ...params,
      item: ReportTypeEnum.MEMBER,
      memberId: memberId,
    });

    return () => {
      const tooltipEl = document.getElementById('chartjs-tooltip');
      if (tooltipEl) {
        document.body.removeChild(tooltipEl);
      }
    };
  }, []);

  return (
    <ScrollArea
      className={'!h-[calc(100vh_-_91px)] relative w-full flex-1 bg-bg-general overflow-x-hidden'}
    >
      <div className={'grid grid-cols-12 mt-3 gap-4 px-[30px]'}>
        {/* <TokenReport
          title={t('group.dashboard.label_chart_member_token')}
          label={t('group.dashboard.label_chart_member_point')}
        /> */}
        <LearnReport title={'Thời gian học'} />
      </div>

      <div className={'grid grid-cols-12 mt-3 gap-4 px-[30px]'}>
        <div className={'col-span-4 sm:col-span-4 md:col-span-3 2xl:col-span-2'}></div>
        <div className={'col-span-8 sm:col-span-8 md:col-span-9 2xl:col-span-10'}>
          <div className={'grid grid-cols-2 gap-6 mt-[50px]'}>
            <div>
              <ListenReport
                title={t('settings.transactions.listen')}
                // @ts-ignore
                reportsData={[]}
                height="h-[250px]"
              />
            </div>
            <div>
              <ReadReport
                title={t('settings.transactions.read')}
                // @ts-ignore
                reportsData={[]}
                height="h-[250px]"
              />
            </div>
          </div>

          <div className={'grid grid-cols-2 gap-6 mt-[50px]'}>
            <div>
              <ExerciseReport
                title={t('course.exercise')}
                // @ts-ignore
                reportsData={[]}
                height="h-[250px]"
              />
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  );
};
export default MemberDetailReportContainer;
