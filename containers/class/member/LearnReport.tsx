'use client';

import React, { useMemo } from 'react';

import { ReportTypeEnum } from '@/configs/ReportTypeEnum';
import { TransactionTypeEnum } from '@/configs/TransactionTypeEnum';
// import { format } from 'date-fns';
import ChartBarSkeleton from '@/containers/class/skeleton/ChartBarSkeleton';
import useReportStore from '@/store/report';
import { ChartItem } from '@/types/component';
import { ReportsEntity } from '@/types/model';
import { map, reduce } from 'lodash';

import Charts from '@/components/Charts/Charts';

import useChartConfig from '@/hooks/Ent/useChartConfig';
import useReportLearns from '@/hooks/Ent/useReportLearns';
import useReportTotalLearns from '@/hooks/Ent/useReportTotalLearns';

const LearnReport = ({ title }) => {
  const { makeBarDateTimeLearn } = useChartConfig();
  const { params, loading } = useReportStore();
  // console.log('params', params);
  const { reportsData, isLoading } = useReportLearns(
    {
      member_id: params.memberId,
      type: ReportTypeEnum.LEARN,
      start_at: params.start_day,
      end_at: params.end_day,
    },
    loading || params.start_day === 0 || params.end_day === 0
  );

  const { reportsData: reportsDataTotal } = useReportTotalLearns(
    {
      member_id: params.memberId,
      start_at: params.start_day,
      end_at: params.end_day,
    },
    loading || params.start_day === 0 || params.end_day === 0
  );
  // console.log('reportsData', reportsData);
  const [charts, columns] = useMemo(() => {
    const colors = ['BLUE'];
    const charts: ChartItem[] = makeBarDateTimeLearn(reportsData, colors);
    // console.log('charts', charts);
    //tổng hợp theo ngày
    const groupedByDay = reduce(
      reportsData,
      (acc, item) => {
        if (!acc[item.day]) {
          acc[item.day] = [];
        }
        acc[item.day].push(item);
        return acc;
      },
      {}
    );
    map(groupedByDay, (items: ReportsEntity[], key) => {
      //tổng hợp điểm theo ngày
      const groupedPoint = reduce(
        items,
        (acc, item) => {
          if (!acc[item.transaction_type]) {
            acc[item.transaction_type] = 0;
          }
          acc[item.transaction_type] += item.amount;
          return acc;
        },
        {}
      );
      const index = charts.findIndex((item) => item.date === parseInt(key));
      if (index >= 0) {
        charts[index - 1] = {
          day: key,
          date: parseInt(key),
          BLUE: groupedPoint[TransactionTypeEnum.TRANSACTION_EARN],
          GREEN: groupedPoint[TransactionTypeEnum.TRANSACTION_BURN],
          isActive: true,
        };
      }
    });

    return [charts, colors];
  }, [reportsData]);
  const CustomTooltip = ({ active, payload, label }) => {
    if (!active || !payload || payload.length === 0 || !payload.some((p) => p.value > 0)) {
      return null;
    }
    // const total = payload.reduce((current, item) => item.value + current, 0);

    const genTransactionName = (id) => {
      const ids = {};
      return ids[id] ?? '#';
    };

    return (
      <div className="bg-bg-box p-3 rounded-md shadow-md">
        <p className="font-bold">Ngày {label}</p>
        {payload.reverse().map((entry, index) => (
          <p
            key={index}
            className="text-xs py-1 flex items-center gap-2"
            style={{ color: entry.color }}
          >
            <i className={'w-3 h-3 inline-block'} style={{ backgroundColor: entry.color }}></i>
            {genTransactionName(entry.name)}: {entry.value} Phút
          </p>
        ))}
      </div>
    );
  };

  return (
    <>
      <div className={'col-span-12 sm:col-span-4 md:col-span-3 2xl:col-span-2'}>
        <div className={'grid grid-cols-3 mb-[30px]'}>
          <div className={'col-span-3 flex'}>
            <div className=" px-[6px]">
              <i className="text-[32px] icon-fire-line text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>Số ngày học liên tục</div>
              <span className={'text-[22px] font-medium'}>
                {reportsDataTotal?.continuous_day || 0}
              </span>
            </div>
          </div>
        </div>
        <div className={'grid grid-cols-3 mb-[30px]'}>
          <div className={'col-span-3 flex'}>
            <div className=" px-[6px]">
              <i className="text-[32px] icon-timer-line text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>Thời lượng học tuần</div>
              <span className={'text-[22px] font-medium'}>
                {reportsDataTotal?.time_learn
                  ? Math.max(1, Math.round(reportsDataTotal.time_learn / 60))
                  : 0}{' '}
                <span className="text-xs">phút</span>
              </span>
            </div>
          </div>
        </div>
        <div className={'grid grid-cols-3 mb-[30px]'}>
          <div className={'col-span-3 flex'}>
            <div className=" px-[6px]">
              <i className="text-[32px] icon-newspaper-line text-yellow-100" />
            </div>
            <div>
              <div className={'text-small'}>Bài học trong tuần</div>
              <span className={'text-[22px] font-medium'}>
                {reportsDataTotal?.count_paragraph_finish || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className={'col-span-12 sm:col-span-8 md:col-span-9 2xl:col-span-10'}>
        <div className={'w-full flex justify-between items-center'}>
          <span className={'text-small font-medium text-color-major'}>{title}</span>
        </div>
        <div className={'mt-2'}>
          <div className={'w-full relative h-[350px]'}>
            {isLoading ? (
              <ChartBarSkeleton />
            ) : (
              /*@ts-ignore*/
              <Charts charts={charts} columns={columns} tooltip={CustomTooltip} label={''} />
            )}
          </div>
        </div>
      </div>
    </>
  );
};
export default LearnReport;
