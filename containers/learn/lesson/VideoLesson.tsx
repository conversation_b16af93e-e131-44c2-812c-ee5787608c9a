import LearnTypeEnum from '@/configs/LearnTypeEnum';
import useLearnStore from '@/store/learn';
import classNames from 'classnames';
import ReactPlayer from 'react-player';
import { useListenVideo, formatTime } from '@/hooks/Ent/useListenVideo';

import FinishScreen from '../FinishScreen';
import StartScreen from '../StartScreen';
import LessonVideoControl from './LessonVideoControl';

const Timeline = ({
  currentTime,
  totalDuration,
}: {
  currentTime: number;
  totalDuration: number;
}) => {
  return (
    <div className="absolute bottom-0 left-0 right-0">
      {/* Time display */}
      <div className="absolute bottom-4 left-4 bg-black/70 text-white px-2 py-1 rounded text-sm">
        {formatTime(currentTime)} / {formatTime(totalDuration)}
      </div>
    </div>
  );
};

const SentenceDisplay = ({ sentence, shouldShow }: { sentence: any; shouldShow: boolean }) => {
  if (!sentence || !shouldShow) return null;
  const { setSelectedSentence, showWord } = useLearnStore();

  return (
    <div
      className={classNames(
        'absolute cursor-pointer left-1/2 -translate-x-1/2 bottom-1 bg-black/50 p-6 rounded-lg text-white max-w-2xl w-full',
        {
          'opacity-100': showWord,
          'opacity-30': !showWord,
        }
      )}
    >
      <div
        className="text-xl font-medium text-center"
        onClick={() => setSelectedSentence(sentence)}
      >
        {sentence.content}
      </div>
    </div>
  );
};

export const VideoLesson = () => {
  const {
    // State
    isStartedLearn,
    isFinished,
    currentSentence,
    shouldShowSentence,
    currentTime,
    totalVideoDuration,
    isPlaying,
    autoReading,
    setAutoReading,
    replay,
    setReplay,
    isAtEndOfLastSentence,
    activeTab,
    setActiveTab,
    isLoadingConversations,
    sortedSentences,
    isFinishingCurrentSentence,
    
    // Player reference
    playerRef,
    videoUrl,
    
    // Video event handlers
    handleVideoDuration,
    handleVideoError,
    handleVideoReady,
    
    // Navigation handlers
    handleNextSentence,
    handleBackSentence,
    handleFinish,
    handleLearnAgain,
    handleStartLearn,
    handleRestart,
    handlePostSentencesPauseToggle,
    
    // Utils
    isInPostSentencesPeriod
  } = useListenVideo();

  if (!isStartedLearn) {
    return (
      <StartScreen
        activeTab={activeTab}
        onStart={handleStartLearn}
        setSelectedCharacter={() => {}}
        selectedCharacter={null}
        isLoadingConversation={isLoadingConversations}
      />
    );
  }

  if (isFinished) {
    return (
      <FinishScreen
        handleRestart={handleRestart}
        handleDoNewExercise={() => setActiveTab(LearnTypeEnum.EXERCISE)}
        isExercise={false}
      />
    );
  }

  return (
    <div className="flex flex-col h-[calc(100vh_-100px)] w-full overflow-hidden">
      <div className="flex-1 w-full flex justify-center items-center bg-[#2a2d3c] rounded-[5px] min-h-0">
        <div className="w-full h-full flex flex-col items-center justify-center relative">
          <div className="flex-1 w-full flex items-center justify-center">
            <ReactPlayer
              ref={playerRef}
              url={videoUrl}
              controls={false}
              width="100%"
              height="100%"
              playing={isPlaying}
              style={{
                pointerEvents: 'none',
              }}
              onError={handleVideoError}
              onDuration={handleVideoDuration}
              onReady={handleVideoReady}
              onPlay={() => {
                if (!isPlaying) {
                  if (playerRef.current) {
                    try {
                      const internalPlayer = playerRef.current.getInternalPlayer();
                      if (internalPlayer && typeof internalPlayer.pauseVideo === 'function') {
                        internalPlayer.pauseVideo();
                      }
                    } catch (error) {
                      console.log('🟢 Error forcing pause on unwanted play:', error);
                    }
                  }
                }
              }}
            />
          </div>
          {/* Show pause/play indicator during post-sentences period */}
          {isInPostSentencesPeriod && (
            <div
              className="absolute inset-0 flex items-center justify-center cursor-pointer"
              onClick={handlePostSentencesPauseToggle}
            >
              <div className="bg-black/50 rounded-full p-4 pointer-events-none">
                <i
                  className={`text-white text-4xl ${isPlaying ? 'icon-pause' : 'icon-play'}`}
                  style={{ opacity: 0.7 }}
                />
              </div>
            </div>
          )}
          <Timeline
            currentTime={currentTime}
            totalDuration={totalVideoDuration}
          />
          <div className="flex-shrink-0 w-full">
            <SentenceDisplay sentence={currentSentence} shouldShow={shouldShowSentence} />
          </div>
        </div>
      </div>

      {sortedSentences && sortedSentences.length > 0 && (
        <div className="flex-shrink-0 w-full p-2">
          <div className="w-full">
            <LessonVideoControl
              onNextSentence={handleNextSentence}
              endSentence={!isFinishingCurrentSentence}
              isFinishConversation={isFinished}
              onFinish={handleFinish}
              isLastSentence={isAtEndOfLastSentence}
              onLeanAgain={handleLearnAgain}
              onBackSentence={handleBackSentence}
              autoReading={autoReading}
              setAutoReading={setAutoReading}
              replay={replay}
              setReplay={setReplay}
            />
          </div>
        </div>
      )}
    </div>
  );
};
