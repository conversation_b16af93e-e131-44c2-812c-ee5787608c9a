import { useMemo } from 'react';

import { SPECIAL_DOCUMENT_ID } from '@/configs';
import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { IMAGE_SIZE } from '@/constant';
import { getResizeImageUrl } from '@/helpers';
import { sortSentencesByPosition } from '@/helpers/sentence';
import { useConversationContext } from '@/providers/ConversationProvider';
import useLearnStore from '@/store/learn';
import { SentenceEntity } from '@/types/model';
import { Divider } from '@heroui/react';
import classNames from 'classnames';

import NextImageWithFallback from '@/components/NextImageWithFallback';
import ScrollArea from '@/components/ScrollArea';

import { useListenGallery } from '@/hooks/Ent/useListenGallery';

import FinishScreen from '../FinishScreen';
import StartScreen from '../StartScreen';
import ConversationControl from './ConversationControl';
import { ListenGalleryItem } from './ListenGalleryItem';

export const ListenGallery = () => {
  const { exerciseToken } = useLearnStore();
  const {
    paragraph,
    sentenceGroups,
    sentences,
    activeTab,
    isLoadingConversations,
    listenCurrentId,
    setActiveTab,
  } = useConversationContext();

  const sortedSentenceGroups = useMemo(() => {
    return sentenceGroups?.sort((a, b) => (a.position ?? 0) - (b.position ?? 0));
  }, [sentenceGroups]);

  const sortedSentences = useMemo(() => {
    // First sort by position
    const sorted = sortSentencesByPosition(sentences);

    // Group sentences by sentence_group_id and maintain group order
    const groupedSentences: SentenceEntity[][] = [];

    // Iterate through sorted sentence groups
    sortedSentenceGroups?.forEach((group) => {
      // Find all sentences for this group
      const sentencesInGroup = sorted.filter((sentence) => sentence.sentence_group_id === group.id);
      if (sentencesInGroup.length > 0) {
        groupedSentences.push(sentencesInGroup);
      }
    });

    // Flatten grouped sentences back to array
    return groupedSentences.flat();
  }, [sentences, sortedSentenceGroups]);

  const {
    isFinishLearn,
    handleFinishConversation,
    handleClickGalleryItem,
    activeSentenceGroup,
    isStartedLearn,
    activeCharacter,
    setActiveCharacter,
    isEndSentences,
    handleNextSentence,
    nextSentences,
    currentSentences,
    handleStartListen,
    handleRestart,
    handleBackSentence,
    isLastSentence,
  } = useListenGallery({
    sortedSentences,
    sortedSentenceGroups,
    listenCurrentId,
    paragraph,
  });

  if (!isStartedLearn) {
    return (
      <StartScreen
        activeTab={activeTab}
        onStart={handleStartListen}
        setSelectedCharacter={setActiveCharacter}
        selectedCharacter={activeCharacter}
        isLoadingConversation={isLoadingConversations}
      />
    );
  }

  if (isFinishLearn)
    return (
      <FinishScreen
        handleRestart={handleRestart}
        handleDoNewExercise={() => setActiveTab(LearnTypeEnum.EXERCISE)}
        isExercise={false}
      />
    );
  return (
    <>
      <ScrollArea
        className={classNames('w-full flex pt-[15px]', {
          '!h-[calc(100vh-170px)]':
            paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken === '',
          '!h-[calc(100vh-129px)]':
            paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken !== '',
          '!h-[calc(100vh-127px)]':
            paragraph?.document_id === SPECIAL_DOCUMENT_ID && exerciseToken === '',
          '!h-[calc(100vh-86px)]':
            paragraph?.document_id === SPECIAL_DOCUMENT_ID && exerciseToken !== '',
        })}
        isEnabled={false}
      >
        {/* Sidebar */}
        <ScrollArea
          className={
            'w-[171px] h-full flex-col flex-shrink-0 overflow-y-auto px-[15px] gap-4 bg-bg-general hidden lg:flex'
          }
        >
          {sortedSentenceGroups &&
            sortedSentenceGroups.length > 0 &&
            sortedSentenceGroups.map((sentence_group) => {
              return (
                <div
                  key={sentence_group.id}
                  className={classNames(
                    'w-full relative h-[87px] overflow-hidden flex-shrink-0 rounded-[5px] bg-bg-box cursor-pointer',
                    {
                      'border border-solid border-purple':
                        activeSentenceGroup?.id == sentence_group.id,
                    }
                  )}
                  onClick={() => handleClickGalleryItem(sentence_group)}
                >
                  {sentence_group.image && (
                    <NextImageWithFallback
                      src={getResizeImageUrl({
                        keyx: sentence_group.keyx,
                        size: IMAGE_SIZE.THUMBNAIL,
                        created_at: sentence_group.image_time,
                        format: sentence_group.format,
                      })}
                      alt={''}
                      fill
                      priority
                      className="object-cover w-full h-full"
                    />
                  )}
                </div>
              );
            })}
        </ScrollArea>

        {/* Main Content Area */}
        <div
          className={classNames(
            'flex flex-1 h-full flex-col pl-[15px] lg:pl-0 font-[Helvetica] font-normal space-y-4 relative overflow-hidden pr-[15px] pb-[15px]',
            {
              'justify-center': isFinishLearn,
            }
          )}
        >
          <div className={'w-full flex-shrink-0 h-full relative'}>
            <div className={'w-full h-full relative bg-[#2a2d3c] rounded-[5px] overflow-hidden'}>
              {activeSentenceGroup && activeSentenceGroup.image && (
                <NextImageWithFallback
                  key={activeSentenceGroup.image}
                  src={activeSentenceGroup.image}
                  alt={''}
                  fill
                  sizes={'100%'}
                  priority
                  objectFit={'contain'}
                />
              )}
            </div>
            <div
              className={
                'w-full align-baseline absolute left-0 bottom-5 flex flex-col z-[100] pr-[15px]'
              }
            >
              {/* Only render ListenGalleryItem if there's an active sentence group and sentences to display */}
              {activeSentenceGroup && (nextSentences.length > 0 || currentSentences.length > 0) && (
                <ListenGalleryItem
                  sentences={currentSentences.length > 0 ? currentSentences : nextSentences}
                />
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
      <Divider orientation={'horizontal'} className={'absolute bottom-[71px] bg-color-line'} />

      <div
        className={
          'absolute bottom-0 left-[85px] z-[11] right-0 bg-bg-general block w-full px-[calc((100%_-_615px)_/_2)]'
        }
      >
        <ConversationControl
          isLastSentence={isLastSentence}
          isFinishConversation={isFinishLearn}
          endSentence={isEndSentences}
          onFinish={() => handleFinishConversation()}
          onNextSentence={() => handleNextSentence()}
          onLeanAgain={handleRestart}
          onBackSentence={handleBackSentence}
        />
      </div>
    </>
  );
};
