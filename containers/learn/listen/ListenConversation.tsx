import { useCallback, useEffect, useMemo, useRef } from 'react';

import { SPECIAL_DOCUMENT_ID } from '@/configs';
import { groupConversationSentences } from '@/helpers/sentence';
import { useConversationContext } from '@/providers/ConversationProvider';
import useLearnStore from '@/store/learn';
import { Divider } from '@heroui/react';
import classNames from 'classnames';

import ScrollArea from '@/components/ScrollArea';

import { useListenGroupSentences } from '@/hooks/Ent/useListenGroupSentences';

import FinishScreen from '../FinishScreen';
import StartScreen from '../StartScreen';
import ConversationControl from './ConversationControl';
import ConversationItem from './ConversationItem';

export const ListenConversation = () => {
  const { exerciseToken } = useLearnStore();
  const {
    paragraph,
    sentences: conversations,
    activeTab,
    isLoadingConversations,
    setActiveTab,
    listenCurrentId,
    characters,
  } = useConversationContext();

  // Add ref for scroll area
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Memoize groupSentences with stable dependencies to prevent infinite loops
  const groupSentences = useMemo(() => {
    if (!conversations?.length) return [];
    return groupConversationSentences(conversations);
  }, [
    conversations?.length,
    conversations?.map((s) => s.id).join(','), // Stable string representation of sentence IDs
  ]);

  const {
    isFinishLearn,
    handleFinishConversation,
    handleNextSentence,
    isStartedLearn,
    isEndSentences,
    handleRestart,
    handleBackSentence,
    isLastSentence,
    currentGroupIndex,
    handleStartListen,
  } = useListenGroupSentences({
    groupSentences,
    listenCurrentId,
    paragraph,
  });

  // Scroll to bottom when new groups are rendered
  useEffect(() => {
    if (scrollAreaRef.current && isStartedLearn && currentGroupIndex >= 0) {
      const scrollToBottom = () => {
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTo({
            top: scrollAreaRef.current.scrollHeight,
            behavior: 'smooth',
          });
        }
      };

      // Use setTimeout to ensure DOM has updated
      const timeoutId = setTimeout(scrollToBottom, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [currentGroupIndex, isStartedLearn]);

  // Memoize computed values
  const scrollAreaClassName = useMemo(
    () =>
      classNames('w-full px-[calc((100%_-_615px)_/_2)] mb-[70px]', {
        '!h-[calc(100vh-170px)]':
          paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken === '',
        '!h-[calc(100vh-136px)]':
          paragraph?.document_id !== SPECIAL_DOCUMENT_ID && exerciseToken !== '',
        '!h-[calc(100vh-124px)]':
          paragraph?.document_id === SPECIAL_DOCUMENT_ID && exerciseToken === '',
        '!h-[calc(100vh-137px)]':
          paragraph?.document_id === SPECIAL_DOCUMENT_ID && exerciseToken !== '',
      }),
    [paragraph?.document_id, exerciseToken]
  );

  const handleDoNewExercise = useCallback(() => {
    setActiveTab(activeTab);
  }, [setActiveTab, activeTab]);

  const groupsToRender = useMemo(() => {
    if (currentGroupIndex < 0) return [];
    return Array.from({ length: currentGroupIndex + 1 }, (_, index) => index);
  }, [currentGroupIndex]);

  if (!isStartedLearn) {
    return (
      <StartScreen
        activeTab={activeTab}
        onStart={handleStartListen}
        setSelectedCharacter={() => {}}
        selectedCharacter={null}
        isLoadingConversation={isLoadingConversations}
      />
    );
  }

  if (isFinishLearn)
    return (
      <FinishScreen
        handleRestart={handleRestart}
        handleDoNewExercise={handleDoNewExercise}
        isExercise={false}
      />
    );

  return (
    <>
      <ScrollArea className={scrollAreaClassName} isEnabled ref={scrollAreaRef}>
        <div
          className={classNames(
            'flex flex-col font-[Helvetica] font-normal space-y-4 relative overflow-hidden pt-8 pr-8',
            {
              'justify-center': isFinishLearn,
            }
          )}
        >
          {/* Display groups from 0 to currentGroupIndex (inclusive) */}
          {/* If currentGroupIndex is -1, don't display any groups */}
          {groupsToRender.map((groupIndex) => {
            if (!groupSentences[groupIndex]) return null;
            return (
              <ConversationItem
                characters={characters}
                currentGroupIndex={groupIndex}
                key={groupIndex}
                sentences={groupSentences[groupIndex]}
                isLastItem={groupIndex === groupSentences.length - 1}
                className={classNames({
                  'opacity-80': groupSentences.length - 3 === groupIndex,
                  'opacity-60': groupSentences.length - 2 === groupIndex,
                  'opacity-10': groupSentences.length - 1 === groupIndex,
                })}
              />
            );
          })}
        </div>
      </ScrollArea>
      <Divider orientation={'horizontal'} className={'absolute bottom-[71px] bg-color-line'} />

      <div
        className={
          'absolute bottom-0 left-0 right-0 bg-bg-general block w-full px-[calc((100%_-_615px)_/_2)]'
        }
      >
        <ConversationControl
          isLastSentence={isLastSentence}
          isFinishConversation={isFinishLearn}
          endSentence={isEndSentences}
          onFinish={handleFinishConversation}
          onNextSentence={handleNextSentence}
          onLeanAgain={handleRestart}
          onBackSentence={handleBackSentence}
        />
      </div>
    </>
  );
};
