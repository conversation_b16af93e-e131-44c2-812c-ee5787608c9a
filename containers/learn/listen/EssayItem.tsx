'use client';

import { memo, useCallback } from 'react';

import classNames from 'classnames';
import { map } from 'lodash';
import useLearnStore from 'store/learn';
import { EssayItemProps } from 'types/component';
import { SentenceEntity } from 'types/model';

const EssayItem = ({ sentences, isLastItem, currentPlayingSentenceIndex = -1 }: EssayItemProps) => {
  const { showWord, setSelectedSentence } = useLearnStore();
  const handleClick = useCallback(
    (essay: SentenceEntity) => {
      setSelectedSentence(essay);
    },
    [setSelectedSentence]
  );

  return (
    <div className={classNames('space-y-1 text-[0.9375rem] max-w-[510px]')}>
      {sentences &&
        map(sentences, (essay, index) => (
          <span
            key={essay.id}
            className={classNames(
              'rounded-sm pr-1 first:-ml-0 cursor-pointer col-span-6 relative transition-all ease-in-out duration-300 hover:bg-[#6e79d67d] font-svn',
              {
                'bg-[#6e79d67d]': currentPlayingSentenceIndex === index,
              }
            )}
            onClick={() => handleClick(essay)}
            dangerouslySetInnerHTML={{ __html: essay.content }}
          />
        ))}
    </div>
  );
};

const areEqual = (prevProps: EssayItemProps, nextProps: EssayItemProps) => {
  if (prevProps.sentences.length !== nextProps.sentences.length) {
    return false;
  }

  const prevIds = prevProps.sentences.map((s) => s.id).join(',');
  const nextIds = nextProps.sentences.map((s) => s.id).join(',');
  if (prevIds !== nextIds) {
    return false;
  }

  return (
    prevProps.currentPlayingSentenceIndex === nextProps.currentPlayingSentenceIndex &&
    prevProps.isLastItem === nextProps.isLastItem
  );
};

export default memo(EssayItem, areEqual);
