'use client';
import PlayAudio from 'containers/learn/listen/PlayAudio';
import { SpeakingLeftItemProps } from 'types/component';
import classNames from 'classnames';
import { map } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import useLearnStore from 'store/learn';
import Avatar from 'components/Avatar';
import useSpeakingStore from 'store/speaking';
import { Howl, Howler } from 'howler';
import { SentenceProcessEnum } from 'configs/SentenceProcessEnum';

const SpeakingItemLeft = ({
  sentences,
  characters,
  className,
  isLastItem,
  isPause,
  isStartLearn,
  setStartLearn
}: SpeakingLeftItemProps) => {
  const { showWord } = useLearnStore();
  const { volume, setSentenceProcess, setSentence } = useSpeakingStore();
  const [soundIndex, setSoundIndex] = useState(0);
  const sounds = useRef<Howl[]>([]);

  const { setSelectedSentence } = useLearnStore();
  useEffect(() => {
    setSoundIndex(0);
    console.log('init left');
    if (!isLastItem) return;
    setSentence(sentences[0]);
  }, []);

  useEffect(() => {
    if (!isLastItem) return;
    if(isStartLearn) {
      setStartLearn();
      setSentenceProcess(SentenceProcessEnum.FINISH);
      return;
    }
    handlePlaySound();
  }, [sentences]);

  useEffect(() => {
    if (!isLastItem) return;
    playSound(soundIndex);
  }, [soundIndex]);

  const handlePlaySound = () => {
    const listHowl: Howl[] = [];
    sentences?.forEach((sentence, index) => {
      const audio = sentence.audios?.[0]?.url || '';
      listHowl[index] = new Howl({
        src: [audio],
        onend: () => {
          handleEnd(index);
        },
        onerror: () => {
          handleEnd(index);
        },
      });
    });
    sounds.current = listHowl;
  };

  const handleEnd = (index: number) => {
    setSentence(sentences[index]);
    if (index + 1 < sounds.current.length) {
      setSoundIndex(index + 1);
    } else {
      setSentenceProcess(SentenceProcessEnum.FINISH);
    }
    //sounds.current[index].unload();
  };

  const playSound = (index: number) => {
    if (sounds.current[index]) {
      sounds.current[index].play();
    }
  };

  useEffect(() => {
    sounds.current[soundIndex]?.playing();
    sounds.current?.map((sound, index) => {
      if (index !== soundIndex && sound.playing()) {
        sound.stop();
      }
    });
    if (!sounds.current[soundIndex] || !isLastItem) return;
    if (isPause) {
      sounds.current[soundIndex].pause();
    } else {
      sounds.current[soundIndex].play();
    }
  }, [isPause]);

  useEffect(() => {
    Howler.volume(volume / 100);
  }, [volume]);

  const character = useMemo(
    () => characters?.find((item) => item.id === sentences[0]?.character_id),
    [characters, sentences],
  );

  return (
    <div className={'flex items-end mb-4'}>
      <div className={'flex flex-col space-y-1 text-[0.9375rem] mx-2 order-1 items-start'}>
        <div className="px-4 text-[0.75rem] text-color-minor">{character?.fullname || ''}</div>
        {sentences &&
          map(sentences, (sentence, index) => (
            <div
              key={index}
              className={classNames(
                'flex items-center group col-span-6 relative pr-8 max-w-[410px]',
              )}
            >
              <div
                onClick={() => setSelectedSentence(sentence)}
                className={classNames(
                  'px-4 cursor-pointer py-1 order-0 pr-6 relative rounded-[23px] inline-block shadow-small text-color-major bg-bg-box',
                  {
                    'rounded-tl-md': index > 0 && sentences.length > 1,
                    'rounded-bl-md': index < sentences.length - 1,
                  },
                )}
              >
                <span
                  className={classNames(
                    className,
                    'transition-all ease-in-out duration-[1s] hover:!opacity-100',
                    {
                      '!opacity-100': showWord,
                      // @ts-ignore
                    },
                  )}
                  dangerouslySetInnerHTML={{ __html: sentence.content || '' }}
                />
              </div>
              <PlayAudio
                key={`audio-${sentence.id}`}
                onFinishAudio={() => {}}
                audios={sentence.audios}
                isLeftConversation={true}
              />
            </div>
          ))}
      </div>
      <Avatar name={sentences[0].character_id?.toString()} size={32} className={'order-0'} />
    </div>
  );
};
export default SpeakingItemLeft;
