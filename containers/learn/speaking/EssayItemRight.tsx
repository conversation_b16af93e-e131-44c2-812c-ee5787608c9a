'use client';

import React, { useEffect, useState } from 'react';

import NavigationModeEnum from '@/configs/NavigationModeEnum';
import { RecordProcessEnum } from '@/configs/RecordProcessEnum';
import classNames from 'classnames';
import { SentenceProcessEnum } from 'configs/SentenceProcessEnum';
import { map } from 'lodash';
import useLearnStore from 'store/learn';
import useSpeakingStore from 'store/speaking';
import { SpeakingItemRightProps } from 'types/component';
import { SentenceEntity } from 'types/model';

const EssayItem = ({
  speaking,
  className,
  isLastRecord,
}: {
  speaking: SentenceEntity;
  className?: string;
  isLastRecord: boolean;
}) => {
  const { showWord, setSelectedSentence } = useLearnStore();
  const [scoreColor, setScoreColor] = useState<string>('');
  const { setSentence, sentenceScore, recordProcess } = useSpeakingStore();

  useEffect(() => {
    if (isLastRecord) {
      setSentence(speaking);
    } else {
      setScoreColor(speaking.color || '');
    }
  }, []);

  useEffect(() => {
    if (sentenceScore && sentenceScore.sentence_id === speaking.id) {
      setScoreColor(sentenceScore.color);
    }
  }, [sentenceScore]);

  return (
    <div className={classNames('flex-0 relative !mt-0 mb-1 text-[0.9375rem]')}>
      <div
        onClick={() => setSelectedSentence(speaking)}
        className={classNames('px-1 cursor-pointer py-1 relative inline-block text-color-major')}
      >
        <span
          className={classNames(
            className,
            'transition-all ease-in-out duration-[0.2s] hover:!opacity-100',
            {
              'border-b border-red':
                scoreColor === 'red' &&
                (!isLastRecord || (isLastRecord && recordProcess !== RecordProcessEnum.PROCESS)),
              'border-b border-primary':
                scoreColor === 'green' &&
                (!isLastRecord || (isLastRecord && recordProcess !== RecordProcessEnum.PROCESS)),
              'border-b border-yellow':
                scoreColor === 'yellow' &&
                (!isLastRecord || (isLastRecord && recordProcess !== RecordProcessEnum.PROCESS)),
              '!opacity-100': showWord,
              '!opacity-5': isLastRecord && !showWord,
            }
          )}
        >
          {!isLastRecord || (isLastRecord && recordProcess !== RecordProcessEnum.PROCESS) ? (
            speaking.content
          ) : (
            <span className="opacity-0">{speaking.content}</span>
          )}
        </span>
      </div>
    </div>
  );
};

const EssayItemRight = ({
  sentences,
  isLastItem,
  className,
  forwardMode,
  isStartLearn,
  setStartLearn,
}: SpeakingItemRightProps) => {
  const [rightSentences, setRightSentences] = useState<Array<SentenceEntity>>([]);
  const { recordProcess, setSentenceProcess, sentence, setRecordProcess } = useSpeakingStore();
  const { currentSentenceId } = useLearnStore();

  useEffect(() => {
    if (!isLastItem) {
      setRightSentences([...sentences]);
    } else {
      if (recordProcess === RecordProcessEnum.INIT && forwardMode === NavigationModeEnum.NEXT) {
        handleListSentence();
      }
    }
  }, [recordProcess, forwardMode]);

  useEffect(() => {
    if (forwardMode === NavigationModeEnum.BACK && isLastItem) {
      const currentSentenceIndex = sentences.findIndex((item) => item.id === sentence?.id);
      if (currentSentenceIndex >= 0) {
        setRightSentences(sentences.slice(0, currentSentenceIndex + 1));
      }
    }
  }, [forwardMode, sentence]);

  const handleListSentence = () => {
    const lengthOfRightSentences = rightSentences.length || 0;
    if (lengthOfRightSentences >= sentences.length && forwardMode === NavigationModeEnum.NEXT) {
      setSentenceProcess(SentenceProcessEnum.FINISH);
      return;
    }

    let foundCurrentSentence = false;
    const positionCurrentSentence = sentences.findIndex((item) => item.id === sentence?.id);
    if (positionCurrentSentence > -1 && forwardMode === NavigationModeEnum.BACK) {
      setRightSentences([]);
      for (const sentenceItem of sentences) {
        //setSentence(sentenceItem);
        setRightSentences((prevSentences) => [...prevSentences, sentenceItem]);
        if (sentenceItem.id === currentSentenceId) {
          foundCurrentSentence = true;
          setSentenceProcess(SentenceProcessEnum.PROCESS);
          break;
        }
      }
      if (!foundCurrentSentence) {
        setSentenceProcess(SentenceProcessEnum.FINISH);
      }
    } else {
      const nextSentence = sentences[lengthOfRightSentences] || null;
      console.log(
        'right sentence ',
        lengthOfRightSentences,
        currentSentenceId,
        positionCurrentSentence,
        isStartLearn
      );
      if (isStartLearn) {
        setStartLearn();
      }

      if (positionCurrentSentence > -1 && isStartLearn) {
        if (positionCurrentSentence === sentences.length - 1) {
          setRightSentences([...sentences]);
          setSentenceProcess(SentenceProcessEnum.FINISH);
          setRecordProcess(RecordProcessEnum.INIT);
        } else {
          setRightSentences([...sentences.slice(0, positionCurrentSentence + 2)]);
          setRecordProcess(RecordProcessEnum.PROCESS);
        }

        return;
      }
      if (nextSentence) {
        setRightSentences((prevState) => [...prevState, nextSentence]);
        setRecordProcess(RecordProcessEnum.PROCESS);
      }
    }
  };

  return (
    <div className={'flex items-start justify-start mb-0 text-[0.9375rem] mx-2'}>
      {rightSentences &&
        map(rightSentences, (speaking, index, array) => (
          <EssayItem
            key={speaking.id}
            speaking={speaking}
            isLastRecord={speaking.id === array[rightSentences.length - 1].id && isLastItem}
            className={className}
          />
        ))}
    </div>
  );
};
export default EssayItemRight;
