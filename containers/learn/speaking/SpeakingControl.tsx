'use client';
import VolumeControl from 'containers/learn/VolumeControl';
import React from 'react';
import { SpeakingControlProps } from 'types/component';
import SentencesControl from '@/containers/learn/speaking/SpeakingControl/SentencesControl';
import SubmitAction from '@/containers/learn/speaking/SpeakingControl/SubmitAction';

const SpeakingControl = ({
  showTooltipRecord = false,
  setIsPauseSpeak,
  toggleRecording,
  handleBackSentence,
  stopMicrophone,
  isPauseSpeak,
  isSendingRecord,
  isPaused,
  isStartMicrophone,
  transcript,
  hasCompletedAllSpeakings,
  handleFinishSpeaking,
}: SpeakingControlProps) => {


  return (
    <div className={'flex items-center justify-between w-full px-[calc((100%_-_615px)_/_2)]'}>
      <VolumeControl />
      <SentencesControl isPaused={isPaused}
        isPauseSpeak={isPauseSpeak}
        toggleRecording={toggleRecording}
        handleBackSentence={handleBackSentence}
        isSendingRecord={isSendingRecord}
        showTooltipRecord={showTooltipRecord}
        setIsPauseSpeak={setIsPauseSpeak}
        isStartMicrophone={isStartMicrophone}
        transcript={transcript}
        hasCompletedAllSpeakings={hasCompletedAllSpeakings}
      />

      <SubmitAction isSendingRecord={isSendingRecord} stopMicrophone={stopMicrophone} handleFinishSpeaking={handleFinishSpeaking} />
    </div>
  );
};
export default SpeakingControl;
