'use client';

import { useEffect, useMemo } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { useConversationContext } from '@/providers/ConversationProvider';
import useBalanceStore from '@/store/balance';
import Button from 'components/Button';
import Image from 'components/Image';
import { SPECIAL_DOCUMENT_ID } from 'configs';
import { StatusEnum } from 'configs/StatusEnum';
import Character from 'containers/learn/speaking/character/Character';
import { useTranslations } from 'next-intl';
import useLearnStore from 'store/learn';
import { type Character as CharacterInterface } from 'types/model';
import { useEventListener } from 'usehooks-ts';

// --- Types ---
interface StartScreenProps {
  activeTab: string;
  isLoadingConversation: boolean;
  onStart: () => void;
  selectedCharacter: CharacterInterface | null;
  setSelectedCharacter: (character: CharacterInterface) => void;
}

// --- Components ---
const LessonHeader = ({ title, imageUrl }: { title?: string; imageUrl?: string }) => (
  <>
    <h5 className="text-color-major text-[0.9375rem] py-0 mb-2.5 text-center font-[SVN-Poppins-Regular]">
      {title}
    </h5>
    <div className="mb-3">
      <Image
        src={imageUrl || ''}
        classNames={{ wrapper: 'bg-[length:383px_245px]' }}
        className="mx-auto !bg-bg-box border-none rounded-md"
        width={383}
        height={245}
        alt={title || 'Lesson Image'}
      />
    </div>
  </>
);

const NotReadyToLearnInfo = ({
  currentTitle,
  currentKeyx,
}: {
  currentTitle?: string;
  currentKeyx?: string;
}) => {
  console.log('🚀 ~ currentKeyx:', currentKeyx);
  const t = useTranslations('learn');

  return (
    <>
      <h6 className="text-normal text-red mb-2">{t('warningStart')}</h6>
      <h5 className="text-color-major text-[0.9375rem] mb-5 text-center font-[SVN-Poppins-Regular]">
        {currentTitle}
      </h5>
      {currentKeyx && (
        <Link
          className="bg-[#00BF55] text-white h-[2.1875rem] leading-[2.1875rem] text-base rounded-medium px-8 font-[Inter] cursor-pointer !shadow-[0px_2px_0px_0px_#01AE4E]"
          href={`/learn/${currentKeyx}`}
        >
          {t('btnGoToCurrentParagraph')}
        </Link>
      )}
    </>
  );
};

const CharacterSelector = ({
  characters,
  selectedCharacterId,
  onSelectCharacter,
}: {
  characters: CharacterInterface[];
  selectedCharacterId?: number;
  onSelectCharacter: (character: CharacterInterface) => void;
}) => {
  const t = useTranslations('learn');

  return (
    <div className="flex flex-col justify-center items-center mt-12">
      <span className="text-center pb-10">{t('choose_character')}</span>
      <div className="flex justify-center w-full gap-4">
        {characters.map((item) => (
          <Character
            key={item.id}
            onClick={onSelectCharacter}
            character={item}
            selectedCharacterId={selectedCharacterId || 0}
          />
        ))}
      </div>
    </div>
  );
};

const StartButton = ({
  isDisabled,
  onClick,
  id = 'start-button-container',
}: {
  isDisabled: boolean;
  onClick: () => void;
  id?: string;
}) => {
  const t = useTranslations('learn');

  return (
    <div id={id}>
      <Button color="primary" isDisabled={isDisabled} onClick={onClick} size="lg">
        {t('btnStart')}
      </Button>
      <div className="text-color-minor mt-1 text-[10px]">Enter</div>
    </div>
  );
};

const StartActionSection = ({
  balanceStatus,
  balanceString,
  isSpeakingTab,
  selectedCharacter,
  onStart,
  buttonId = 'start-button-container',
  isLoadingConversation,
}: {
  balanceStatus: StatusEnum;
  balanceString: string;
  isSpeakingTab: boolean;
  isLoadingConversation: boolean;
  selectedCharacter: CharacterInterface | null;
  onStart: () => void;
  buttonId?: string;
}) => {
  const t = useTranslations('learn');

  return (
    <>
      <div className="mt-5 mb-2 text-color-major text-[13px]">{t('note_start_learn')}</div>

      {balanceStatus !== StatusEnum.ON && balanceString !== '' && (
        <div className="mt-5 mb-2 text-danger text-[13px]">{t('note_not_enough_token')}</div>
      )}

      {balanceStatus === StatusEnum.ON && (
        <StartButton
          isDisabled={(isSpeakingTab && !selectedCharacter) || isLoadingConversation}
          onClick={onStart}
          id={buttonId}
        />
      )}
    </>
  );
};

// --- Main Component ---
const StartScreen = ({
  onStart,
  activeTab,
  setSelectedCharacter,
  selectedCharacter,
  isLoadingConversation,
}: StartScreenProps) => {
  const { exerciseToken } = useLearnStore();
  const { balanceStatus, balanceString } = useBalanceStore();
  const { paragraph, paragraphs, characters } = useConversationContext();
  const router = useRouter();
  const buttonId = 'start-button-container';

  // Derived state
  const isSpecialDocument = useMemo(() => {
    return paragraph?.document_id === SPECIAL_DOCUMENT_ID;
  }, [paragraph?.document_id]);

  const notLearnedParagraph = useMemo(() => {
    if (!paragraphs || !paragraphs?.length) return false;

    const activeParagraphIndex = paragraphs?.findIndex((item) => item.id === paragraph?.id);
    if (!activeParagraphIndex) return false;

    const currentProgressIndex = paragraphs?.findIndex(
      (item) => item.id === paragraph?.paragraph_current_id
    );

    return currentProgressIndex === -1 || currentProgressIndex < activeParagraphIndex;
  }, [paragraph, paragraphs]);

  const isApproved = paragraph?.process_approve === StatusEnum.ON;
  const showWarning = (notLearnedParagraph && !isSpecialDocument && !exerciseToken) || !isApproved;
  const showStartOptions =
    (!notLearnedParagraph || isSpecialDocument || exerciseToken) && isApproved;
  const isSpeakingTab = activeTab === LearnTypeEnum.SPEAKING;
  const showCharacterSelection = isSpeakingTab && characters?.length > 1;

  useMemo(() => {
    if(isSpeakingTab && characters?.length === 1) {
      setSelectedCharacter(characters[0]);
      console.log('🚀 ~ showCharacterSelection:', characters[0]);
    }
  }, [isSpeakingTab, characters]);

  // Handle start learnin
  // Event handling
  useEventListener(
    'keydown',
    (e) => {
      if ( e.key === 'Enter' && showStartOptions )
      {
        if (isLoadingConversation) return;
        if (showWarning) {
          router.push(`/learn/${paragraph.ParagraphCurrentKeyx}`);
        } else {
          if(showCharacterSelection && !selectedCharacter) return;
          onStart();
        }
      }
    },
    undefined,
    { capture: true }
  );

  // Focus the button on mount when available
  useEffect(() => {
    if (showStartOptions) {
      const buttonElement = document.getElementById(buttonId)?.querySelector('button');
      if (buttonElement) {
        buttonElement.focus();
      }
    }
  }, [showStartOptions, buttonId]);

  if (!paragraph) return null;

  return (
    <div className="flex w-full flex-1 items-center justify-center flex-col">
      {/* Header with Title and Image */}
      <LessonHeader title={paragraph.title} imageUrl={paragraph.image} />

      {/* Warning section when not ready to learn */}
      {showWarning && (
        <NotReadyToLearnInfo
          currentTitle={paragraph.ParagraphCurrentTitle}
          currentKeyx={paragraph.ParagraphCurrentKeyx}
        />
      )}

      {/* Start options section */}
      {showStartOptions && (
        <div className="mt-2.5 text-center mb-0 w-full">
          {/* Character selection for speaking mode */}
          {showCharacterSelection && (
            <CharacterSelector
              characters={characters}
              selectedCharacterId={selectedCharacter?.id}
              onSelectCharacter={setSelectedCharacter}
            />
          )}

          {/* Start button and related information */}
          <StartActionSection
            balanceStatus={balanceStatus}
            balanceString={balanceString || ''}
            isSpeakingTab={isSpeakingTab}
            selectedCharacter={selectedCharacter}
            onStart={onStart}
            buttonId={buttonId}
            isLoadingConversation={isLoadingConversation}
          />
        </div>
      )}
    </div>
  );
};

export default StartScreen;
