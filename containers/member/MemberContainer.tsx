'use client';

import React, { useEffect, useState } from 'react';

import { useParams, useRouter } from 'next/navigation';

import { OTP_MEMBER_LENGTH } from '@/configs';
import EntRouters from '@/configs/EntRouters';
import LoginMemberSkeleton from '@/containers/member/skeleton/loginMemberSkeleton';
import useFavouriteStore from '@/store/favourite';
import { setCookie } from 'cookies-next';
import { signIn, signOut } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import OtpInput from 'react-otp-input';

import Avatar from '@/components/Avatar';

import useAuth from '@/hooks/Ent/useAuth';

import Button from '../../components/Button';

const MemberContainer = ({ member }) => {
  const router = useRouter();
  const params = useParams();
  const { error } = params;
  const [OTP, setOTP] = useState('');
  const t = useTranslations();
  const [, setErrMessage] = useState<string | null>('');
  const { setMessage, errorMessage } = useAuth();
  const { setFavourites } = useFavouriteStore();

  useEffect(() => {
    // Logout tất cả các token cũ khi vào trang
    setFavourites([]);
    signOut({ redirect: false });
  }, []);

  const handleChangeOTP = (value: string) => {
    setOTP(value);
    // Kiểm tra nếu độ dài của OTP đạt đủ điều kiện để submit
    if (value.length === OTP_MEMBER_LENGTH) {
      handleVerifyOtp(value); // Gọi hàm submit khi điều kiện đạt được
    }
  };
  const handleVerifyOtp = async (OTP) => {
    setMessage('');
    if (OTP.length !== OTP_MEMBER_LENGTH) {
      setMessage(t('auth.passWrong'));
      return;
    }
    await signIn('member-login', {
      redirect: false,
      token: member.token,
      password: OTP,
      csrfToken: '',
    }).then((res) => {
      if (typeof res?.error !== 'undefined' && res?.error !== null) {
        //setMessage(t('auth.' + (res?.error || '')));
        setMessage(t('auth.passWrong'));
      } else {
        setCookie('ent.member-part', 'ak', {
          maxAge: 24 * 60 * 60 * 30, // 1 ngày
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
        });
        localStorage.setItem('ent.app-name', 'langenter');
        router.push(EntRouters.home);
      }
    });
  };

  const errorsHandle: any = {
    AccessDenied: 'AccessDeniedX.',
  };
  useEffect(() => {
    setErrMessage(error && (errorsHandle[error.toString()] ?? errorsHandle.default));
  }, [error]);

  return (
    <>
      {member ? (
        <div className="container mx-auto px-4 h-full">
          <div className="flex content-center items-center justify-center h-full">
            <div className="w-full lg:w-4/12 px-4">
              <div className="relative border-0 flex flex-col min-w-0 break-words w-full mb-2 ">
                <div className="flex-auto px-4 lg:px-10  pt-10">
                  <div className="flex items-center justify-center">
                    {/*image={ member.avatar }*/}
                    <Avatar
                      size={128}
                      name={member?.fullname}
                      className={'rounded-full w-[128px] h-[128px] object-center'}
                    />
                  </div>
                  <div className={'text-color-major text-center my-2'}>{t('onboard.hello')}</div>
                  <div className={'text-center text-xl mb-[40px]'}>{member?.fullname}</div>
                  <div className="relative w-full my-3 text-center">
                    <div className={'text-color-major'}>{t('auth.password')}</div>
                  </div>
                  <div className={'text-center mb-[40px]'}>
                    <OtpInput
                      value={OTP}
                      onChange={handleChangeOTP}
                      numInputs={OTP_MEMBER_LENGTH}
                      renderSeparator={''}
                      inputType={'tel'}
                      renderInput={(props) => <input {...props} />}
                      placeholder="0000"
                      containerStyle={{
                        fontSize: '40px',
                        display: 'inline-flex',
                      }}
                      inputStyle={{
                        color: 'Rgb(var(--color-major))',
                        width: '3rem',
                        margin: '0 5px',
                        border: '1px solid Rgb(var(--color-border))',
                        outline: 0,
                        borderRadius: '8px',
                        fontSize: 32,
                        background: 'var(--bg-box)',
                      }}
                    />
                  </div>
                  {errorMessage !== '' && (
                    <div className={'mt-3 mb-4 text-red text-center'}>{errorMessage}</div>
                  )}
                  {/*<div className={'text-color-major mt-3 mb-4 text-[red]'}>Bạn đã nhập sai 3 lần</div>
                                <div className={'text-color-major mt-2 mb-1 text-[#3f3934] text-[12px]'}>Hãy nhập lại sau 10 phút</div>
                                <div className={'text-color-major text-[#3f3934] text-[12px]'}>Chờ 1 ngày hoặc Liên hệ với tài khoản Nguyễn Đức Ninh để cài đặt lại mật khẩu</div>*/}
                  <div className={'text-center'}>
                    <Button type={'submit'} color={'primary'} size={'lg'}>
                      {t('auth.btnLogin')}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <LoginMemberSkeleton />
      )}
    </>
  );
};

export default MemberContainer;
