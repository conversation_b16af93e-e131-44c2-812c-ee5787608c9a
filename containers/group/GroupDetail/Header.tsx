'use client';

import React, { useCallback } from 'react';

import { useParams } from 'next/navigation';

import classNames from 'classnames';
import Button from 'components/Button';
import GroupAddPopup from 'components/ServicePopup/GroupAddPopup';
import { ROLE_ALL, ROLE_APPROVE } from 'configs';
import AppHeader from 'containers/layout/AppHeader';
import { useTranslations } from 'next-intl';
import useHeaderStore from 'store/header';

import { useSession } from '@/hooks/useSession';

interface HeaderProps {
  breadcrumbs: string[];
  group_id: number;
  mutate?: (() => void) | null;
}

interface ModalData {
  title: string;
  type: number;
}

/**
 * Component for rendering breadcrumbs
 */
const Breadcrumbs: React.FC<{ items: string[] }> = ({ items }) => {
  if (items.length === 0) return null;

  return (
    <>
      {items.map((item, index) => (
        <div key={index} className="flex items-center">
          {item}
          {index < items.length - 1 && <i className="icon-arrow-right-fill text-medium" />}
        </div>
      ))}
    </>
  );
};

/**
 * Component for rendering action buttons
 */
const ActionButtons: React.FC<{
  groupId: number;
  sessionData: any;
  tabActivated: string;
  onOpenModal: (type: number, title: string) => void;
}> = ({ groupId, sessionData, tabActivated, onOpenModal }) => {
  const t = useTranslations();

  if (!groupId) {
    if (sessionData?.user?.role_id === ROLE_APPROVE) {
      return (
        <Button
          onClick={() => onOpenModal(1, t('group.add'))}
          size="xs"
          variant="bordered"
          color="default"
          className="float-end px-2"
        >
          <i className={classNames('icon-add text-normal')} /> {t('group.button.addOrganization')}
        </Button>
      );
    }
    return null;
  }

  if (
    !tabActivated &&
    (sessionData?.user?.role_id === ROLE_APPROVE ||
      sessionData?.user?.type === ROLE_APPROVE ||
      sessionData?.user?.role_id === ROLE_ALL)
  ) {
    return (
      <Button
        onClick={() => onOpenModal(2, t('group.button.addClass'))}
        size="xs"
        variant="bordered"
        color="default"
        className="float-end px-2 ml-2.5"
      >
        <i className={classNames('icon-add text-normal')} /> {t('group.button.addClass')}
      </Button>
    );
  }

  return null;
};

/**
 * Header component for the Group Detail page
 */
const Header: React.FC<HeaderProps> = ({ breadcrumbs, group_id, mutate }) => {
  const { data: sessionData } = useSession();
  const params = useParams();
  const tabActivated = params.tabs?.toString() || '';
  const { title } = useHeaderStore();
  const t = useTranslations();

  const [open, setOpen] = React.useState<boolean>(false);
  const [modalData, setModalData] = React.useState<ModalData>({ title: '', type: 1 });

  const handleClose = useCallback(() => {
    setOpen(false);
    mutate?.();
  }, [mutate]);

  const handleOpen = (type: number, modalTitle: string) => {
    setModalData({ type, title: modalTitle });
    setOpen(true);
  };

  // Determine title text based on breadcrumbs or store
  const titleText = breadcrumbs.length === 0 ? title || t('group.title') : null;

  return (
    <AppHeader bottom="0px">
      <div className="h-[43px] px-[30px] w-full grid grid-cols-12 items-center">
        <div className="col-span-6 flex items-center">
          <div className="flex gap-x-2">
            <i className="icon-group text-[16px]" />
            {titleText || <Breadcrumbs items={breadcrumbs} />}
          </div>
        </div>

        <div className="col-span-6 flex flex-grow-0 items-center justify-end">
          <ActionButtons
            groupId={group_id}
            sessionData={sessionData}
            tabActivated={tabActivated}
            onOpenModal={handleOpen}
          />
        </div>
      </div>

      <GroupAddPopup
        title={modalData.title}
        type={modalData.type}
        open={open}
        groupId={group_id}
        onOpen={setOpen}
        onClose={handleClose}
      />
    </AppHeader>
  );
};

export default Header;
