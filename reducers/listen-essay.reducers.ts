import { SentenceEntity } from '@/types/model';

export interface IListenEssayState {
  isStartedLearn: boolean;
  isFinishLearn: boolean;
  isEndSentences: boolean;
  isLastSentence: boolean;
  currentGroupIndex: number;
  currentSentenceIndex: number;
  currentPlayingSentenceIndex: number;
  allSentences: SentenceEntity[];
}

export enum ListenEssayActionType {
  SET_STARTED_LEARN = 'SET_STARTED_LEARN',
  SET_FINISH_LEARN = 'SET_FINISH_LEARN',
  SET_END_SENTENCES = 'SET_END_SENTENCES',
  SET_LAST_SENTENCE = 'SET_LAST_SENTENCE',
  SET_CURRENT_PLAYING_SENTENCE_INDEX = 'SET_CURRENT_PLAYING_SENTENCE_INDEX',
  SET_ALL_SENTENCES = 'SET_ALL_SENTENCES',
  LEARN_AGAIN = 'LEARN_AGAIN',
  FINISH_CONVERSATION = 'FINISH_CONVERSATION',
  INITIALIZE = 'INITIALIZE',
  NEXT_SENTENCE = 'NEXT_SENTENCE',
  PREV_SENTENCE = 'PREV_SENTENCE',
}

export type ListenEssayAction =
  | { type: ListenEssayActionType.SET_STARTED_LEARN; payload: boolean }
  | { type: ListenEssayActionType.SET_FINISH_LEARN; payload: boolean }
  | { type: ListenEssayActionType.SET_END_SENTENCES; payload: boolean }
  | { type: ListenEssayActionType.SET_LAST_SENTENCE; payload: boolean }
  | { type: ListenEssayActionType.SET_CURRENT_PLAYING_SENTENCE_INDEX; payload: number }
  | { type: ListenEssayActionType.SET_ALL_SENTENCES; payload: SentenceEntity[] }
  | { type: ListenEssayActionType.LEARN_AGAIN; payload: { allSentences: SentenceEntity[] } }
  | { type: ListenEssayActionType.FINISH_CONVERSATION }
  | {
      type: ListenEssayActionType.INITIALIZE;
      payload: {
        groupSentences: Array<SentenceEntity[]>;
        allSentences: SentenceEntity[];
        listenCurrentId?: number;
      };
    }
  | {
      type: ListenEssayActionType.NEXT_SENTENCE;
      payload: {
        allSentences: SentenceEntity[];
        groupSentences: Array<SentenceEntity[]>;
      };
    }
  | {
      type: ListenEssayActionType.PREV_SENTENCE;
      payload: {
        allSentences: SentenceEntity[];
        groupSentences: Array<SentenceEntity[]>;
      };
    };

export const listenEssayReducer = (
  state: IListenEssayState,
  action: ListenEssayAction
): IListenEssayState => {
  switch (action.type) {
    case ListenEssayActionType.SET_STARTED_LEARN:
      return { ...state, isStartedLearn: action.payload };
    case ListenEssayActionType.SET_FINISH_LEARN:
      return { ...state, isFinishLearn: action.payload };
    case ListenEssayActionType.SET_END_SENTENCES:
      return { ...state, isEndSentences: action.payload };
    case ListenEssayActionType.SET_LAST_SENTENCE:
      return { ...state, isLastSentence: action.payload };
    case ListenEssayActionType.SET_CURRENT_PLAYING_SENTENCE_INDEX:
      return { ...state, currentPlayingSentenceIndex: action.payload };
    case ListenEssayActionType.SET_ALL_SENTENCES:
      return { ...state, allSentences: action.payload };
    case ListenEssayActionType.FINISH_CONVERSATION:
      return { ...state, isFinishLearn: true };
    case ListenEssayActionType.LEARN_AGAIN:
      return {
        ...state,
        isFinishLearn: false,
        isStartedLearn: true,
        isEndSentences: true,
        isLastSentence: false,
        currentGroupIndex: -1,
        currentSentenceIndex: -1,
        currentPlayingSentenceIndex: 0,
        allSentences: action.payload.allSentences,
      };
    case ListenEssayActionType.INITIALIZE: {
      const { groupSentences, allSentences, listenCurrentId } = action.payload;
      let currentGroupIndex = -1;
      let currentSentenceIndex = -1;
      let isLastSentence = false;

      if (listenCurrentId) {
        // Find which sentence is the current one
        const foundIndex = allSentences.findIndex((s) => s.id === listenCurrentId);

        // Only proceed if it's not the first sentence (index > 0)
        // This ensures we show nothing if listenCurrentId matches the first sentence
        if (foundIndex > 0) {
          // Start with the previous sentence instead of the current one
          currentSentenceIndex = Math.max(0, foundIndex - 1);
          const currentSentence = allSentences[currentSentenceIndex];

          // Find which group contains this sentence
          for (let i = 0; i < groupSentences.length; i++) {
            const groupIndex = groupSentences[i].findIndex((s) => s.id === currentSentence.id);
            if (groupIndex >= 0) {
              currentGroupIndex = i;
              break;
            }
          }

          isLastSentence = currentSentenceIndex === allSentences.length - 1;
        }
      }

      return {
        ...state,
        currentGroupIndex,
        currentSentenceIndex,
        currentPlayingSentenceIndex: 0,
        isEndSentences: true,
        isLastSentence,
        allSentences,
      };
    }
    case ListenEssayActionType.NEXT_SENTENCE: {
      const { allSentences, groupSentences } = action.payload;
      const nextSentenceIndex = state.currentSentenceIndex + 1;

      // If we're at the beginning or reached the end, do nothing
      if (nextSentenceIndex >= allSentences.length) {
        return {
          ...state,
          isLastSentence: true,
        };
      }

      const nextSentence = allSentences[nextSentenceIndex];
      let nextGroupIndex = state.currentGroupIndex;

      // Find which group the next sentence belongs to
      for (let i = 0; i < groupSentences.length; i++) {
        const sentenceInGroup = groupSentences[i].find((s) => s.id === nextSentence.id);
        if (sentenceInGroup) {
          nextGroupIndex = i;
          break;
        }
      }

      return {
        ...state,
        currentSentenceIndex: nextSentenceIndex,
        currentGroupIndex: nextGroupIndex,
        currentPlayingSentenceIndex: 0,
        isLastSentence: nextSentenceIndex === allSentences.length - 1,
        isEndSentences: true,
      };
    }
    case ListenEssayActionType.PREV_SENTENCE: {
      const { allSentences, groupSentences } = action.payload;
      const prevSentenceIndex = state.currentSentenceIndex - 1;

      // If we're at the first sentence (index 0), go back to the initial state (-1)
      if (state.currentSentenceIndex <= 0) {
        return {
          ...state,
          currentSentenceIndex: -1,
          currentGroupIndex: -1,
          isLastSentence: false,
          isEndSentences: true,
        };
      }

      const prevSentence = allSentences[prevSentenceIndex];
      let prevGroupIndex = state.currentGroupIndex;

      // Find which group the previous sentence belongs to
      for (let i = 0; i < groupSentences.length; i++) {
        const sentenceInGroup = groupSentences[i].find((s) => s.id === prevSentence.id);
        if (sentenceInGroup) {
          prevGroupIndex = i;
          break;
        }
      }

      return {
        ...state,
        currentSentenceIndex: prevSentenceIndex,
        currentGroupIndex: prevGroupIndex,
        currentPlayingSentenceIndex: 0,
        isLastSentence: false,
        isEndSentences: true,
      };
    }
    default:
      return state;
  }
};
