import { SentenceEntity } from '@/types/model';

export interface IListenGroupSentenceState {
  isStartedLearn: boolean;
  isFinishLearn: boolean;
  isEndSentences: boolean;
  isLastSentence: boolean;
  nextSentences: SentenceEntity[];
  currentGroupIndex: number;
  currentSentenceIndex: number;
  currentPlayingSentenceIndex: number; // Index of currently playing sentence within current group
}

export enum ListenGroupSentenceActionType {
  SET_STARTED_LEARN = 'SET_STARTED_LEARN',
  SET_FINISH_LEARN = 'SET_FINISH_LEARN',
  SET_END_SENTENCES = 'SET_END_SENTENCES',
  SET_LAST_SENTENCE = 'SET_LAST_SENTENCE',
  SET_CURRENT_SENTENCES = 'SET_CURRENT_SENTENCES',
  SET_NEXT_SENTENCES = 'SET_NEXT_SENTENCES',
  SET_CURRENT_PLAYING_SENTENCE_INDEX = 'SET_CURRENT_PLAYING_SENTENCE_INDEX',
  LEARN_AGAIN = 'LEARN_AGAIN',
  FINISH_CONVERSATION = 'FINISH_CONVERSATION',
  INITIALIZE = 'INITIALIZE',
  NEXT_GROUP = 'NEXT_GROUP',
  PREV_GROUP = 'PREV_GROUP',
}

export type ListenGroupSentenceAction =
  | { type: ListenGroupSentenceActionType.SET_STARTED_LEARN; payload: boolean }
  | { type: ListenGroupSentenceActionType.SET_FINISH_LEARN; payload: boolean }
  | { type: ListenGroupSentenceActionType.SET_END_SENTENCES; payload: boolean }
  | { type: ListenGroupSentenceActionType.SET_LAST_SENTENCE; payload: boolean }
  | { type: ListenGroupSentenceActionType.SET_CURRENT_SENTENCES; payload: SentenceEntity[] }
  | { type: ListenGroupSentenceActionType.SET_NEXT_SENTENCES; payload: SentenceEntity[] }
  | { type: ListenGroupSentenceActionType.SET_CURRENT_PLAYING_SENTENCE_INDEX; payload: number }
  | { type: ListenGroupSentenceActionType.LEARN_AGAIN; payload: SentenceEntity[] }
  | { type: ListenGroupSentenceActionType.FINISH_CONVERSATION }
  | {
      type: ListenGroupSentenceActionType.INITIALIZE;
      payload: {
        groupSentences: Array<SentenceEntity[]>;
        listenCurrentId?: number;
      };
    }
  | {
      type: ListenGroupSentenceActionType.NEXT_GROUP;
      payload: { groupSentences: Array<SentenceEntity[]> };
    }
  | {
      type: ListenGroupSentenceActionType.PREV_GROUP;
      payload: { groupSentences: Array<SentenceEntity[]> };
    };

export const listenGroupSentenceReducer = (
  state: IListenGroupSentenceState,
  action: ListenGroupSentenceAction
): IListenGroupSentenceState => {
  switch (action.type) {
    case ListenGroupSentenceActionType.SET_STARTED_LEARN:
      return { ...state, isStartedLearn: action.payload };
    case ListenGroupSentenceActionType.SET_FINISH_LEARN:
      return { ...state, isFinishLearn: action.payload };
    case ListenGroupSentenceActionType.SET_END_SENTENCES:
      return { ...state, isEndSentences: action.payload };
    case ListenGroupSentenceActionType.SET_LAST_SENTENCE:
      return { ...state, isLastSentence: action.payload };
    case ListenGroupSentenceActionType.SET_NEXT_SENTENCES:
      return { ...state, nextSentences: action.payload };
    case ListenGroupSentenceActionType.SET_CURRENT_PLAYING_SENTENCE_INDEX:
      return { ...state, currentPlayingSentenceIndex: action.payload };
    case ListenGroupSentenceActionType.FINISH_CONVERSATION:
      return { ...state, isFinishLearn: true };
    case ListenGroupSentenceActionType.LEARN_AGAIN:
      return {
        ...state,
        isFinishLearn: false,
        isStartedLearn: true,
        isEndSentences: true,
        isLastSentence: false,
        nextSentences: action.payload,
        currentGroupIndex: -1,
        currentSentenceIndex: 0,
        currentPlayingSentenceIndex: 0,
      };
    case ListenGroupSentenceActionType.INITIALIZE: {
      const { groupSentences, listenCurrentId } = action.payload;
      let currentGroupIndex = -1; // Start at -1 to show blank page initially
      let nextSentences: SentenceEntity[] = [];
      let isLastSentence = false;

      if (listenCurrentId) {
        // Find which group contains the listenCurrentId
        for (let groupIndex = 0; groupIndex < groupSentences.length; groupIndex++) {
          const sentenceIndex = groupSentences[groupIndex].findIndex(
            (s) => s.id === listenCurrentId
          );
          if (sentenceIndex >= 0) {
            // Set currentGroupIndex to the exact group containing listenCurrentId
            currentGroupIndex = groupIndex;
            // Set nextSentences to the current group (ready to play)
            nextSentences = groupSentences[groupIndex] || [];
            break;
          }
        }
        isLastSentence = currentGroupIndex === groupSentences.length - 1;
      } else {
        // No listenCurrentId - start fresh
        currentGroupIndex = -1;
        nextSentences = groupSentences[0] || [];
      }

      return {
        ...state,
        nextSentences,
        currentGroupIndex,
        currentSentenceIndex: 0,
        currentPlayingSentenceIndex: 0,
        isEndSentences: true,
        isLastSentence: isLastSentence,
      };
    }
    case ListenGroupSentenceActionType.NEXT_GROUP: {
      const { groupSentences } = action.payload;

      // Special case: if currentGroupIndex is -1, find the correct group based on nextSentences
      if (state.currentGroupIndex === -1) {
        // Find which group index the nextSentences belongs to
        let targetGroupIndex = 0;

        if (state.nextSentences.length > 0) {
          const firstSentenceId = state.nextSentences[0].id;

          for (let groupIndex = 0; groupIndex < groupSentences.length; groupIndex++) {
            const sentenceIndex = groupSentences[groupIndex].findIndex(
              (s) => s.id === firstSentenceId
            );

            if (sentenceIndex >= 0) {
              targetGroupIndex = groupIndex;
              break;
            }
          }
        }

        // Prepare next group sentences (if exists)
        const nextGroupIndex = targetGroupIndex + 1;
        const nextGroupSentences =
          nextGroupIndex < groupSentences.length ? groupSentences[nextGroupIndex] : [];

        return {
          ...state,
          nextSentences: nextGroupSentences,
          currentGroupIndex: targetGroupIndex,
          currentSentenceIndex: 0,
          currentPlayingSentenceIndex: 0,
        };
      }

      const nextGroupIndex = state.currentGroupIndex + 1;

      // Check if we can move to next group
      if (nextGroupIndex < groupSentences.length) {
        const nextGroupSentences = groupSentences[nextGroupIndex] || [];

        return {
          ...state,
          nextSentences: nextGroupSentences,
          currentGroupIndex: nextGroupIndex,
          currentSentenceIndex: 0,
          currentPlayingSentenceIndex: 0,
        };
      } else {
        // No more groups available - just stop, don't auto-finish
        // User needs to manually click finish button
        return {
          ...state,
          nextSentences: [],
          isEndSentences: true,
        };
      }
    }
    case ListenGroupSentenceActionType.PREV_GROUP: {
      const { groupSentences } = action.payload;
      const prevGroupIndex = state.currentGroupIndex - 1;

      // Always allow going back, even to index -1 (which means no groups displayed)
      if (prevGroupIndex >= 0) {
        const prevGroupSentences = groupSentences[prevGroupIndex] || [];

        return {
          ...state,
          nextSentences: prevGroupSentences,
          currentGroupIndex: prevGroupIndex,
          currentSentenceIndex: 0,
          currentPlayingSentenceIndex: 0,
          isEndSentences: true, // Set to true so next action will play current group
        };
      } else {
        // Going back from index 0 - set to -1 (no groups displayed)
        return {
          ...state,
          nextSentences: groupSentences[0] || [], // Set first group as nextSentences for when user clicks next
          currentGroupIndex: -1,
          currentSentenceIndex: 0,
          currentPlayingSentenceIndex: 0,
          isEndSentences: true, // Set to true so next action will play the first group
        };
      }
    }
    default:
      return state;
  }
};
