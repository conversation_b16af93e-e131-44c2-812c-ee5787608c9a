import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { ApiEndpoints } from 'configs/ApiEndpoints';
import { default as EntRouters } from 'configs/EntRouters';
import { default as ErrorCode } from 'configs/ErrorCode';

export { ApiEndpoints, EntRouters, ErrorCode };

export const ACCESS_TOKEN = 'accessToken';
export const REFRESH_TOKEN = 'refreshToken';
export const COOKIE_PHONE = 'ent.userPhone';
export const COOKIE_FULL_NAME = 'ent.userFullName';
export const DEVICE_ID = 'ent.deviceId';
export const OTP_LENGTH = 6;
export const OTP_MEMBER_LENGTH = 4;
export const ROLE_APPROVE = 2;
export const TYPE_LOGIN_MEMBER = 'member';
export const TYPE_LOGIN_ACCOUNT = 'account';
export const ROLE_ALL = 1;

export const LEARN_TAB = [
  {
    id: LearnTypeEnum.LISTEN,
    title: 'Nghe',
  },
  {
    id: LearnTypeEnum.SPEAKING,
    title: 'Nói',
  },
  {
    id: LearnTypeEnum.EXERCISE,
    title: 'Bài tập',
  },
];

export const SENTENCE_GROUPS_TABS = [
  {
    id: LearnTypeEnum.LISTEN,
    title: 'Nghe',
  },
  {
    id: LearnTypeEnum.EXERCISE,
    title: 'Bài tập',
  },
];

export const SIDEBAR_MAIN_PARENT_ID = 8;
export const SIDEBAR_SETTING_PARENT_ID = 12;
export const HOMEPAGE_PARENT_ID = 21;
export const POPULAR_KEYWORD_PARENT_ID = 120;
export const SAVED_SEARCH_KEYWORD = 'searchHistory';
export const ITEM_ESSAY = 'essay';
export const ITEM_CONVERSATION = 'conversation';
export const DEFAULT_RECORD_PER_PAGE = 25;
export const HISTORY_PAGE_PARENT_ID = 21;
export const SENTENCE = 'sentence';
export const WORD = 'word';
export const PARAGRAPH = 'paragraph';
export const HOMEPAGE_PANEL_ID = 132;
export const OTP_EXPIRED_TIME_IN_SECOND = 120;
export const TIMEOUT_SPEAKING = 3;
export const SPECIAL_DOCUMENT_ID = -1;
export const SPECIAL_COURSE_ID = -1;
export const ITEM_GALLERY = 'gallery';
export const BALANCE_MIN = 1;
export const GENDERS = [
  { id: 'male', title: 'Nam' },
  { id: 'female', title: 'Nữ' },
];
export const AGES = [
  { id: 'kid', title: 'Trẻ em' },
  { id: 'youth', title: 'Thanh niên' },
  { id: 'adult', title: 'Trung niên' },
  { id: 'old', title: 'Cao tuổi' },
];
export const ACCENTS = [
  { id: 'american', title: 'Người Mỹ' },
  { id: 'british', title: 'Người Anh' },
];
export const SIDEBAR_SETTING_MEMBER_PARENT_ID = 219;
export const SPEAK_CHECK_TYPE = 2;
