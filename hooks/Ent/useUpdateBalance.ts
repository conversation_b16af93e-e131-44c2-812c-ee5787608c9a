import { BALANCE_MIN } from '@/configs';
import { StatusEnum } from '@/configs/StatusEnum';
import useBalanceStore from '@/store/balance';
import { useLocale } from 'next-intl';

const updateBalance = () => {
  const { setBalance, setBalanceString, setBalanceStatus, balance: currentBalance } = useBalanceStore();
  const locale = useLocale();
  return (balance?: number) => {
    const _balance = balance ?? currentBalance;
    setBalanceString(_balance.toLocaleString(locale, { minimumFractionDigits: 0 }));
    setBalance(_balance);
    setBalanceStatus(_balance >= BALANCE_MIN ? StatusEnum.ON : StatusEnum.OFF);
  };
};

export default updateBalance;
