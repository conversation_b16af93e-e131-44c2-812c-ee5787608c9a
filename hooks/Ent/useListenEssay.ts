import { useCallback, useEffect, useMemo, useReducer } from 'react';

import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import {
  IListenEssayState,
  ListenEssayActionType,
  listenEssayReducer,
} from '@/reducers/listen-essay.reducers';
import useBalanceStore from '@/store/balance';
import useLearnStore from '@/store/learn';
import useSpeakingStore from '@/store/speaking';
import { ParagraphEntity, SentenceEntity } from '@/types/model';
import { useGlobalAudioPlayer } from 'react-use-audio-player';

import { useSession } from '../useSession';
import {
  useBackSentenceMutation,
  useEndListenMutation,
  useSaveCurrentSentenceMutation,
  useStartListenMutation,
} from './useSentence';

export const useListenEssay = ({
  groupSentences,
  listenCurrentId,
  paragraph,
}: {
  groupSentences: Array<SentenceEntity[]>;
  listenCurrentId?: number;
  paragraph?: ParagraphEntity;
}) => {
  const { data: session } = useSession();
  const { exerciseToken, transactionInfo } = useLearnStore();
  const { balanceStatus } = useBalanceStore();
  const { volume, autoReading, setAutoReading, replay } = useSpeakingStore();
  const { load, stop } = useGlobalAudioPlayer();

  // Create a flattened array of all sentences for easier navigation
  const allSentences = useMemo(() => {
    return groupSentences.flatMap((group) => group);
  }, [groupSentences]);

  const initialState: IListenEssayState = {
    isStartedLearn: false,
    isFinishLearn: false,
    isEndSentences: true,
    isLastSentence: false,
    currentGroupIndex: -1,
    currentSentenceIndex: -1,
    currentPlayingSentenceIndex: 0,
    allSentences: [],
  };

  const [state, dispatch] = useReducer(listenEssayReducer, initialState);

  const {
    isStartedLearn,
    isFinishLearn,
    isEndSentences,
    isLastSentence,
    currentGroupIndex,
    currentSentenceIndex,
    currentPlayingSentenceIndex,
  } = state;

  // Update allSentences when groupSentences changes
  useEffect(() => {
    dispatch({
      type: ListenEssayActionType.SET_ALL_SENTENCES,
      payload: allSentences,
    });
  }, [allSentences]);

  // Memoize groupSentences with stable dependencies
  const memoizedGroupSentences = useMemo(() => {
    return groupSentences;
  }, [
    groupSentences.length,
    // Create stable hash of sentence IDs for each group
    groupSentences
      .map((group, groupIndex) => `${groupIndex}:${group.map((s) => s.id).join(',')}`)
      .join('|'),
  ]);

  useEffect(() => {
    if (!memoizedGroupSentences?.length) return;

    dispatch({
      type: ListenEssayActionType.INITIALIZE,
      payload: {
        groupSentences: memoizedGroupSentences,
        allSentences,
        listenCurrentId,
      },
    });
  }, [listenCurrentId, memoizedGroupSentences, allSentences]);

  const startListenMutation = useStartListenMutation();

  const handleStartListen = useCallback(() => {
    if (!paragraph || balanceStatus !== StatusEnum.ON) return;
    dispatch({ type: ListenEssayActionType.SET_STARTED_LEARN, payload: true });
    startListenMutation.mutate({
      document_id: paragraph?.document_id || 0,
      paragraph_id: paragraph?.id || 0,
      course_id: paragraph?.course_id || 0,
      activeTab: LearnTypeEnum.LISTEN,
      member_exercise_token: exerciseToken || '',
    });
  }, [paragraph, balanceStatus, exerciseToken, startListenMutation]);

  const endListenMutation = useEndListenMutation();
  const handleFinishConversation = () => {
    if (replay) {
      if (allSentences.length > 0) {
        dispatch({
          type: ListenEssayActionType.LEARN_AGAIN,
          payload: { allSentences },
        });

        // Play first sentence after resetting
        if (allSentences[0]) {
          setTimeout(() => {
            playSentence(allSentences[0]);
          }, 100);
        }
      }
      return;
    }

    if (transactionInfo) {
      endListenMutation.mutate({
        transactionInfo: transactionInfo,
      });
    }
    dispatch({ type: ListenEssayActionType.FINISH_CONVERSATION });
  };

  const handleRestart = () => {
    if (autoReading) return;

    // Stop current audio if playing
    stop();

    dispatch({
      type: ListenEssayActionType.LEARN_AGAIN,
      payload: { allSentences },
    });
    if (allSentences[0]) {
      backSentenceMutation.mutate({
        activeTab: LearnTypeEnum.LISTEN,
        sentence_id: allSentences[0].id,
        paragraph_id: paragraph?.id || 0,
        member_exercise_token: exerciseToken || '',
      });
    }
  };

  const backSentenceMutation = useBackSentenceMutation();

  const handleBackSentence = () => {
    if (balanceStatus !== StatusEnum.ON) return;

    // Don't do anything if we're already at the initial state
    if (currentSentenceIndex === -1) return;

    // Dispatch the action to go back
    dispatch({
      type: ListenEssayActionType.PREV_SENTENCE,
      payload: { allSentences, groupSentences: memoizedGroupSentences },
    });

    // For the first sentence (index 0), save its state as the back sentence
    // if (currentSentenceIndex === 0 && allSentences[0]) {
    //   backSentenceMutation.mutate({
    //     activeTab: LearnTypeEnum.LISTEN,
    //     sentence_id: allSentences[0].id,
    //     paragraph_id: paragraph?.id || 0,
    //     member_exercise_token: exerciseToken || '',
    //   });
    // }
    // For other sentences, use the previous sentence
    if (currentSentenceIndex > 0) {
      const prevIndex = currentSentenceIndex - 1;
      if (prevIndex >= 0 && allSentences[prevIndex]) {
        const prevSentence = allSentences[prevIndex];

        backSentenceMutation.mutate({
          activeTab: LearnTypeEnum.LISTEN,
          sentence_id: prevSentence.id,
          paragraph_id: paragraph?.id || 0,
          member_exercise_token: exerciseToken || '',
        });
      }
    }

    // Remove auto-play when going back - user can manually play if needed
  };

  const handleNextSentence = () => {
    if (!isEndSentences || balanceStatus != StatusEnum.ON || isFinishLearn) {
      console.log('🚀 ~ handleNextSentence ~ early return due to conditions');
      return;
    }

    dispatch({ type: ListenEssayActionType.SET_LAST_SENTENCE, payload: false });

    // If we haven't started yet, play the first sentence
    if (currentSentenceIndex === -1) {
      dispatch({
        type: ListenEssayActionType.NEXT_SENTENCE,
        payload: { allSentences, groupSentences: memoizedGroupSentences },
      });

      if (allSentences.length > 0) {
        setTimeout(() => {
          playSentence(allSentences[0]);
        }, 100);
      }
      return;
    }

    // Check if there are more sentences available
    const nextIndex = currentSentenceIndex + 1;

    if (nextIndex >= allSentences.length) {
      // End of all sentences
      if (replay) {
        // Restart from beginning with replay option
        dispatch({
          type: ListenEssayActionType.LEARN_AGAIN,
          payload: { allSentences },
        });

        setTimeout(() => {
          dispatch({
            type: ListenEssayActionType.NEXT_SENTENCE,
            payload: { allSentences, groupSentences: memoizedGroupSentences },
          });

          if (allSentences.length > 0) {
            playSentence(allSentences[0]);
          }
        }, 100);
        return;
      }

      // No more sentences and no replay - turn off autoReading
      if (autoReading) {
        setAutoReading(false);
      }
      return;
    }

    // Move to next sentence
    dispatch({
      type: ListenEssayActionType.NEXT_SENTENCE,
      payload: { allSentences, groupSentences: memoizedGroupSentences },
    });

    // Play the next sentence
    const nextSentence = allSentences[nextIndex];
    if (nextSentence) {
      setTimeout(() => {
        playSentence(nextSentence);
      }, 100);
    }
  };

  const saveCurrentSentenceMutation = useSaveCurrentSentenceMutation();

  const playSentence = (sentence: SentenceEntity) => {
    if (isFinishLearn) return;

    stop();
    dispatch({ type: ListenEssayActionType.SET_END_SENTENCES, payload: false });

    const audioUrl = sentence.audios?.[0]?.url;

    if (!audioUrl) {
      dispatch({ type: ListenEssayActionType.SET_END_SENTENCES, payload: true });

      // If this is the last sentence
      if (sentence.id === allSentences[allSentences.length - 1]?.id) {
        dispatch({ type: ListenEssayActionType.SET_LAST_SENTENCE, payload: true });

        // If auto-reading is on and replay is off, turn off auto-reading
        if (autoReading && !replay) {
          setAutoReading(false);
        }
      }

      // Save progress
      setTimeout(() => {
        saveCurrentSentenceMutation.mutate({
          document_id: paragraph?.document_id || 0,
          paragraph_id: paragraph?.id || 0,
          sentence_ids: [sentence.id],
          member_id: session?.user.id,
          member_exercise_token: exerciseToken,
        });
      }, 100);

      return;
    }

    load(audioUrl, {
      autoplay: true,
      initialVolume: volume / 100,
      onend: () => {
        dispatch({ type: ListenEssayActionType.SET_END_SENTENCES, payload: true });

        // Check if this is the last sentence
        if (sentence.id === allSentences[allSentences.length - 1]?.id) {
          dispatch({ type: ListenEssayActionType.SET_LAST_SENTENCE, payload: true });

          // If auto-reading is on and replay is off, turn off auto-reading
          if (autoReading && !replay) {
            setAutoReading(false);
          }
        }

        // Save progress after playing the sentence
        setTimeout(() => {
          saveCurrentSentenceMutation.mutate({
            document_id: paragraph?.document_id || 0,
            paragraph_id: paragraph?.id || 0,
            sentence_ids: [sentence.id],
            member_id: session?.user.id,
            member_exercise_token: exerciseToken,
          });
        }, 100);
      },
    });
  };

  useEffect(() => {
    if (autoReading && isEndSentences) {
      handleNextSentence();
    }
  }, [autoReading, isEndSentences]);

  // Calculate the current group's sentences for display
  const currentGroupSentences = useMemo(() => {
    if (currentGroupIndex >= 0 && currentGroupIndex < memoizedGroupSentences.length) {
      return memoizedGroupSentences[currentGroupIndex];
    }
    return [];
  }, [currentGroupIndex, memoizedGroupSentences]);

  // Get the current sentence
  const currentSentence = useMemo(() => {
    if (currentSentenceIndex >= 0 && currentSentenceIndex < allSentences.length) {
      return allSentences[currentSentenceIndex];
    }
    return undefined;
  }, [currentSentenceIndex, allSentences]);

  return {
    isStartedLearn,
    isFinishLearn,
    isLastSentence,
    handleFinishConversation,
    handleRestart,
    handleBackSentence,
    handleNextSentence,
    playAudio: playSentence,
    currentSentence,
    isEndSentences,
    groupSentences: memoizedGroupSentences,
    currentGroupIndex,
    currentGroupSentences,
    currentPlayingSentenceIndex,
    handleStartListen,
  };
};
