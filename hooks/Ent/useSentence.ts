import { StatusEnum } from '@/configs/StatusEnum';
import { QUERY_KEY } from '@/constant/query-key';
import useBalanceStore from '@/store/balance';
import useLearnStore from '@/store/learn';
import { TransactionLearnInfo } from '@/types/model';
import axiosConfig from '@/utils/axios.config';
import { UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError, AxiosResponse } from 'axios';
import { ApiEndpoints, ErrorCode } from 'configs';
import { useTranslations } from 'next-intl';
import toast from 'react-hot-toast';
import { ParamsEndListen, ParamsEndSpeak, SentenceUpdateEntity } from 'types/hooks';

import useUpdateBalance from '@/hooks/Ent/useUpdateBalance';

// Define LearnTypeEnum if it's not imported elsewhere
enum LearnTypeEnum {
  SPEAK = 'speak',
  LISTEN = 'listen',
}
export const useStartListenMutation = (
  options?: Omit<
    UseMutationOptions<
      TransactionLearnInfo,
      AxiosError,
      {
        document_id: number;
        course_id: number;
        paragraph_id: number;
        activeTab?: string;
        member_exercise_token?: string;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async (payload) => {
      const { activeTab, ...restParams } = payload;
      const typeValue = activeTab === LearnTypeEnum.LISTEN ? 2 : 1;

      await axiosConfig.post(`${ApiEndpoints.START_PARAGRAPH}`, {
        ...restParams,
      });

      const response = await axiosConfig.post<TransactionLearnInfo>(
        `${ApiEndpoints.MARK_TRANSACTION_START_LEARN}`,
        { type: typeValue }
      );

      return response.data;
    },
    onSuccess: (data) => {
      const learnStore = useLearnStore.getState();
      learnStore.setTransactionInfo(data);
    },
    ...options,
  });
};

export const useBackSentenceMutation = (
  options?: Omit<
    UseMutationOptions<
      AxiosResponse,
      AxiosError,
      {
        activeTab?: string;
        sentence_id: number;
        paragraph_id: number;
        member_exercise_token: string;
        character_id?: number;
      }
    >,
    'mutationFn'
  >
) => {
  return useMutation({
    mutationFn: async (payload: {
      activeTab: LearnTypeEnum;
      sentence_id: number;
      paragraph_id: number;
      member_exercise_token: string;
      character_id?: number;
    }) => {
      const action = payload.activeTab === LearnTypeEnum.LISTEN ? 1 : 2;
      return await axiosConfig.post(`${ApiEndpoints.BACK_SENTENCE}`, {
        action,
        sentence_id: payload.sentence_id,
        paragraph_id: payload.paragraph_id,
        member_exercise_token: payload.member_exercise_token,
        character_id: payload.character_id,
      });
    },
    ...options,
  });
};

export const useSaveCurrentSentenceMutation = (
  options?: Omit<UseMutationOptions<AxiosResponse, AxiosError, ParamsEndListen>, 'mutationFn'>
) => {
  const updateBalance = useUpdateBalance();
  const t = useTranslations();
  const { balanceStatus, setBalanceStatus } = useBalanceStore();

  return useMutation({
    mutationFn: async (payload: ParamsEndListen) => {
      if (balanceStatus != StatusEnum.ON) return;

      const { data } = await axiosConfig.post(ApiEndpoints.END_LISTEN, {
        ...payload,
      });

      if (!data) {
        if ([ErrorCode.NOT_FOUND_BALANCE, ErrorCode.NOT_ENOUGH_BALANCE].includes(data?.message)) {
          setBalanceStatus(StatusEnum.OFF);
          toast.error(t('learn.note_not_enough_token'), {
            duration: 10000,
          });
        }
        throw data;
      }

      if (data && data?.member_token?.quantity) {
        updateBalance(data.member_token.quantity);
      }

      return data;
    },
    ...options,
  });
};

export const useSendSpeakMutation = (
  options?: Omit<UseMutationOptions<AxiosError, AxiosError, ParamsEndSpeak>, 'mutationFn'>
) => {
  const updateBalance = useUpdateBalance();
  const t = useTranslations();
  const { balanceStatus, setBalanceStatus } = useBalanceStore();

  return useMutation({
    mutationFn: async (params: ParamsEndSpeak) => {
      if (balanceStatus !== StatusEnum.ON) {
        throw new Error('Balance status is not active');
      }

      try {
        const response = await axiosConfig.post(ApiEndpoints.END_SPEAK, params, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'x-access-token': params.access_token,
          },
        });

        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data) => {
      updateBalance(data?.member_token?.quantity ?? 0);
    },
    onError: (error: any) => {
      if (
        [ErrorCode.NOT_FOUND_BALANCE, ErrorCode.NOT_ENOUGH_BALANCE].includes(
          error?.response?.data?.message
        )
      ) {
        setBalanceStatus(StatusEnum.OFF);
        toast.error(t('learn.note_not_enough_token'), {
          duration: 10000,
        });
      }
    },
    ...options,
  });
};

export const useUpsertSentencesMutation = (
  options?: Omit<UseMutationOptions<AxiosError, AxiosError, SentenceUpdateEntity[]>, 'mutationFn'>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (sentences: SentenceUpdateEntity[]) => {
      try {
        const response = await axiosConfig.post(ApiEndpoints.SENTENCE_UPDATE, {
          sentences: sentences,
        });

        return response.data;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEY.CONVERSATIONS] });
    },
    ...options,
  });
};
