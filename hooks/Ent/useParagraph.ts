import { QUERY_KEY } from '@/constant/query-key';
import { ParagraphEntity } from '@/types/model';
import axiosConfig from '@/utils/axios.config';
import { useQuery } from '@tanstack/react-query';
import { ApiEndpoints } from 'configs';
import { useSession } from '@/hooks/useSession';
import { useMemo } from 'react';

export const useParagraph = ({ keyx }: { keyx: string }) => {
  const { data: session } = useSession();

  const queryResult = useQuery({
    queryKey: [QUERY_KEY.PARAGRAPH, keyx],
    queryFn: async (): Promise<ParagraphEntity> => {
      try {
        const response = await axiosConfig.get(`${ApiEndpoints.PARAGRAPH_DETAIL}/${keyx}`, {
          headers: {
            'x-access-token': session?.accessToken,
          },
        });
        return response.data;
      } catch (error: any) {
        throw error;
      }
    },
    staleTime: Infinity,
    enabled: !!keyx,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    retry: false,
  });

  // Check if error should redirect to 404
  const shouldRedirectTo404 = useMemo(() => {
    if (!queryResult.error) return false;

    const error = queryResult.error as any;

    // Based on axios interceptor: error.response || error
    // So status should be directly on error object
    const status = error?.status;

    // Redirect to 404 for these error codes
    return status === 404 || status === 400 || status === 500;
  }, [queryResult.error]);

  return {
    ...queryResult,
    shouldRedirectTo404,
  };
};
