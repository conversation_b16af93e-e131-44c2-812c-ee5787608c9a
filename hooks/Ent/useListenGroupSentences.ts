import { useCallback, useEffect, useMemo, useReducer } from 'react';

import LearnTypeEnum from '@/configs/LearnTypeEnum';
import { StatusEnum } from '@/configs/StatusEnum';
import {
  IListenGroupSentenceState,
  ListenGroupSentenceActionType,
  listenGroupSentenceReducer,
} from '@/reducers/listen-group-sentence.reducers';
import useBalanceStore from '@/store/balance';
import useLearnStore from '@/store/learn';
import useSpeakingStore from '@/store/speaking';
import { ParagraphEntity, SentenceEntity } from '@/types/model';
import { useGlobalAudioPlayer } from 'react-use-audio-player';

import { useEndListenMutation } from '@/hooks/Ent/useConversations';

import { useSession } from '../useSession';
import {
  useBackSentenceMutation,
  useSaveCurrentSentenceMutation,
  useStartListenMutation,
} from './useSentence';

export const useListenGroupSentences = ({
  groupSentences,
  listenCurrentId,
  paragraph,
}: {
  groupSentences: Array<SentenceEntity[]>;
  listenCurrentId?: number;
  paragraph?: ParagraphEntity;
}) => {
  const { data: session } = useSession();
  const { exerciseToken, transactionInfo } = useLearnStore();
  const { balanceStatus } = useBalanceStore();
  const { volume, autoReading, setAutoReading, replay } = useSpeakingStore();
  const { load, stop } = useGlobalAudioPlayer();

  const initialState: IListenGroupSentenceState = {
    isStartedLearn: false,
    isFinishLearn: false,
    isEndSentences: true,
    isLastSentence: false,
    nextSentences: [],
    currentGroupIndex: -1,
    currentSentenceIndex: 0,
    currentPlayingSentenceIndex: 0,
  };

  const [state, dispatch] = useReducer(listenGroupSentenceReducer, initialState);

  const {
    isStartedLearn,
    isFinishLearn,
    isEndSentences,
    isLastSentence,
    nextSentences,
    currentPlayingSentenceIndex,
  } = state;

  // Memoize groupSentences with stable dependencies
  const memoizedGroupSentences = useMemo(() => {
    return groupSentences;
  }, [
    groupSentences.length,
    // Create stable hash of sentence IDs for each group
    groupSentences
      .map((group, groupIndex) => `${groupIndex}:${group.map((s) => s.id).join(',')}`)
      .join('|'),
  ]);

  useEffect(() => {
    if (!memoizedGroupSentences?.length) return;

    dispatch({
      type: ListenGroupSentenceActionType.INITIALIZE,
      payload: {
        groupSentences: memoizedGroupSentences,
        listenCurrentId,
      },
    });
  }, [listenCurrentId, memoizedGroupSentences]);

  const startListenMutation = useStartListenMutation();

  const handleStartListen = useCallback(() => {
    if (!paragraph || balanceStatus !== StatusEnum.ON) return;
    dispatch({ type: ListenGroupSentenceActionType.SET_STARTED_LEARN, payload: true });
    startListenMutation.mutate({
      document_id: paragraph?.document_id || 0,
      paragraph_id: paragraph?.id || 0,
      course_id: paragraph?.course_id || 0,
      activeTab: LearnTypeEnum.LISTEN,
      member_exercise_token: exerciseToken || '',
    });
  }, [paragraph, balanceStatus, exerciseToken, startListenMutation]);

  const endListenMutation = useEndListenMutation();
  const handleFinishConversation = () => {
    if (replay) {
      if (memoizedGroupSentences.length > 0 && memoizedGroupSentences[0].length > 0) {
        dispatch({
          type: ListenGroupSentenceActionType.SET_NEXT_SENTENCES,
          payload: memoizedGroupSentences[0],
        });

        dispatch({
          type: ListenGroupSentenceActionType.SET_CURRENT_SENTENCES,
          payload: [],
        });

        dispatch({
          type: ListenGroupSentenceActionType.SET_FINISH_LEARN,
          payload: false,
        });

        playAudio(memoizedGroupSentences[0]);
      }
      return;
    }
    if (transactionInfo && paragraph) {
      endListenMutation.mutate({
        document_id: paragraph?.document_id,
        course_id: paragraph?.course_id,
        paragraph_id: paragraph?.id,
        member_id: session?.member?.id || 0,
        member_exercise_token: '',
        transactionInfo: transactionInfo,
        access_token: session?.accessToken || '',
      });
    }
    dispatch({ type: ListenGroupSentenceActionType.FINISH_CONVERSATION });
  };

  const handleRestart = () => {
    if (autoReading) return;

    // Stop current audio if playing
    stop();

    // Ensure we always restart with first group
    const firstGroupSentences = memoizedGroupSentences.length > 0 ? memoizedGroupSentences[0] : [];

    dispatch({
      type: ListenGroupSentenceActionType.LEARN_AGAIN,
      payload: firstGroupSentences,
    });
  };

  const backSentenceMutation = useBackSentenceMutation();

  const handleBackSentence = () => {
    if (balanceStatus !== StatusEnum.ON) return;

    dispatch({ type: ListenGroupSentenceActionType.SET_LAST_SENTENCE, payload: false });
    dispatch({
      type: ListenGroupSentenceActionType.PREV_GROUP,
      payload: { groupSentences: memoizedGroupSentences },
    });

    const prevGroupIndex = state.currentGroupIndex - 1;
    // Ensure we handle case when going back from first group
    const prevSentences =
      prevGroupIndex >= 0 ? memoizedGroupSentences[prevGroupIndex] : memoizedGroupSentences[0];

    if (prevSentences.length > 0) {
      backSentenceMutation.mutate({
        activeTab: LearnTypeEnum.LISTEN,
        sentence_id: prevSentences[0].id,
        paragraph_id: paragraph?.id || 0,
        member_exercise_token: exerciseToken || '',
      });
    }

    // No auto-play when going back - just navigate to previous group
    // Set isEndSentences to true so next click will play current group
    dispatch({ type: ListenGroupSentenceActionType.SET_END_SENTENCES, payload: true });
  };

  const handleNextSentence = () => {
    if (!isEndSentences || balanceStatus != StatusEnum.ON || isFinishLearn) {
      console.log('🚀 ~ handleNextSentence ~ early return due to conditions');
      return;
    }

    dispatch({ type: ListenGroupSentenceActionType.SET_LAST_SENTENCE, payload: false });

    // Special case: if currentGroupIndex is -1, move to next group (which is prepared in nextSentences)
    if (state.currentGroupIndex === -1) {
      dispatch({
        type: ListenGroupSentenceActionType.NEXT_GROUP,
        payload: { groupSentences: memoizedGroupSentences },
      });

      // Always play the nextSentences (which are correctly set by LEARN_AGAIN or INITIALIZE)
      if (nextSentences.length > 0) {
        setTimeout(() => {
          playAudio(nextSentences);
        }, 100);
      }
      return;
    }

    // Check if there are more groups available to move to next group
    const nextGroupIndex = state.currentGroupIndex + 1;

    if (nextGroupIndex >= memoizedGroupSentences.length) {
      // Check if replay is enabled - if yes, restart from beginning
      if (replay) {
        // Restart from the very beginning like a fresh start
        const firstGroupSentences = memoizedGroupSentences[0] || [];

        dispatch({
          type: ListenGroupSentenceActionType.LEARN_AGAIN,
          payload: firstGroupSentences,
        });

        // Immediately move to first group and play
        setTimeout(() => {
          dispatch({
            type: ListenGroupSentenceActionType.NEXT_GROUP,
            payload: { groupSentences: memoizedGroupSentences },
          });

          if (firstGroupSentences.length > 0) {
            setTimeout(() => {
              playAudio(firstGroupSentences);
            }, 100);
          }
        }, 100);
        return;
      }

      // No more groups and no replay - turn off autoReading and stop
      if (autoReading) {
        setAutoReading(false);
      }
      return;
    }

    // Always move to next group and play it - no checking current group
    dispatch({
      type: ListenGroupSentenceActionType.NEXT_GROUP,
      payload: { groupSentences: memoizedGroupSentences },
    });

    // Play the next group after dispatch
    const nextGroupSentences = memoizedGroupSentences[nextGroupIndex] || [];
    if (nextGroupSentences.length > 0) {
      setTimeout(() => {
        playAudio(nextGroupSentences);
      }, 100);
    }
  };

  const saveCurrentSentenceMutation = useSaveCurrentSentenceMutation();

  const playAudio = (sentences: SentenceEntity[]) => {
    if (isFinishLearn) return;

    stop();
    dispatch({ type: ListenGroupSentenceActionType.SET_END_SENTENCES, payload: false });
    dispatch({
      type: ListenGroupSentenceActionType.SET_CURRENT_PLAYING_SENTENCE_INDEX,
      payload: 0,
    });

    const listAudio = sentences.map((sentence) => sentence.audios?.[0]?.url);

    if (!listAudio.length) {
      dispatch({ type: ListenGroupSentenceActionType.SET_END_SENTENCES, payload: true });
      return;
    }

    const playAudioAtIndex = (index: number) => {
      if (index >= listAudio.length) {
        dispatch({ type: ListenGroupSentenceActionType.SET_END_SENTENCES, payload: true });

        // Check if this is the last group
        // Get the current sentence being played
        const currentSentence = sentences[index - 1];

        // Find the last sentence in all groups
        const lastSentenceInAllGroups =
          memoizedGroupSentences[memoizedGroupSentences.length - 1]?.slice(-1)[0];

        // Check if current sentence is the last one
        const isLastSentence = currentSentence?.id === lastSentenceInAllGroups?.id;

        if (isLastSentence) {
          dispatch({ type: ListenGroupSentenceActionType.SET_LAST_SENTENCE, payload: true });
          // If replay is not enabled, stop auto reading and mark as last sentence
          if (autoReading && !replay) {
            setAutoReading(false);
          }
        }

        // Save the sentence position after playing finished
        if (sentences.length > 0) {
          setTimeout(() => {
            saveCurrentSentenceMutation.mutate({
              document_id: paragraph?.document_id || 0,
              paragraph_id: paragraph?.id || 0,
              sentence_ids: sentences.map((s) => s.id),
              member_id: session?.user.id,
              member_exercise_token: exerciseToken,
            });
          }, 100);
        }
        return;
      }

      dispatch({
        type: ListenGroupSentenceActionType.SET_CURRENT_PLAYING_SENTENCE_INDEX,
        payload: index,
      });

      if (listAudio[index]) {
        load(listAudio[index], {
          autoplay: true,
          initialVolume: volume / 100,
          onend: () => {
            playAudioAtIndex(index + 1);
          },
        });
      } else {
        playAudioAtIndex(index + 1);
      }
    };

    playAudioAtIndex(0);
  };

  useEffect(() => {
    if (autoReading && isEndSentences) {
      handleNextSentence();
    }
  }, [autoReading, isEndSentences]);

  return {
    isStartedLearn,
    isFinishLearn,
    isLastSentence,
    handleFinishConversation,
    handleRestart,
    handleBackSentence,
    handleNextSentence,
    playAudio,
    nextSentences,
    isEndSentences,
    groupSentences: memoizedGroupSentences,
    currentGroupIndex: state.currentGroupIndex,
    currentPlayingSentenceIndex,
    handleStartListen,
  };
};
