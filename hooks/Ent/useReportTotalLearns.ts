import { useEffect } from 'react';

import { ApiEndpoints } from '@/configs';
import useReportStore from '@/store/report';
import useSWRImmutable from 'swr/immutable';

interface ReportTotalLearnsData {
  continuous_day: number;
  time_learn: number;
  count_paragraph_finish: number;
}

interface UseReportTotalLearnsProps {
  start_at: number;
  end_at: number;
  member_id?: number;
}

const useReportTotalLearns = (params: UseReportTotalLearnsProps, shouldNotFetch: boolean) => {
  const { setLoading } = useReportStore();
  // @ts-ignore
  const queryParamsReportLearn = new URLSearchParams(params);
  const { data, isLoading } = useSWRImmutable(
    shouldNotFetch || !params.member_id
      ? null
      : `${ApiEndpoints.REPORT_TOTAL_LEARNS}?${queryParamsReportLearn}`
  );
  useEffect(() => {
    if (data) {
      setLoading(isLoading);
    }
  }, [isLoading]);

  return {
    reportsData: (data?.data as ReportTotalLearnsData) || null,
    isLoading,
  };
};
export default useReportTotalLearns;
