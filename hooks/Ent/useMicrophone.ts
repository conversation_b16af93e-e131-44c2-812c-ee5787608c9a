import {useEffect, useRef, useReducer, useCallback} from 'react';

import {TIMEOUT_SPEAKING} from 'configs';
import {franc} from 'franc';
import {delay} from 'utils/common';


// Add type declarations for Web Speech API
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }

  interface SpeechRecognitionEvent {
    readonly results: SpeechRecognitionResultList;
  }

  interface SpeechRecognitionResultList {
    readonly length: number;

    item(index: number): SpeechRecognitionResult;

    readonly [index: number]: SpeechRecognitionResult;
  }

  interface SpeechRecognitionResult {
    readonly isFinal: boolean;

    readonly [index: number]: SpeechRecognitionAlternative;
  }

  interface SpeechRecognitionAlternative {
    readonly transcript: string;
    readonly confidence: number;
  }
}

const VOLUME_THRESHOLD = 13; // Ngưỡng âm lượng tối thiểu
const MIN_RECORDING_DURATION = 200; // Minimum recording duration in ms
const DEBOUNCE_DELAY = 100; // Debounce delay between actions in ms

// Định nghĩa các action types
const actionTypes = {
  SET_IS_START_MICROPHONE: 'SET_IS_START_MICROPHONE',
  SET_AUDIO_BLOB: 'SET_AUDIO_BLOB',
  SET_IS_SPEAKING: 'SET_IS_SPEAKING',
  SET_AUDIO_CONTEXT: 'SET_AUDIO_CONTEXT',
  SET_ANALYSER: 'SET_ANALYSER',
  SET_STOP_BY_TIMEOUT: 'SET_STOP_BY_TIMEOUT',
  SET_IS_ENGLISH: 'SET_IS_ENGLISH',
  FORCE_UPDATE: 'FORCE_UPDATE',
  SET_RANDOM: 'SET_RANDOM',
  SET_RECORD_TIME: 'SET_RECORD_TIME',
  SET_RECORD_TIME_INTERVAL: 'SET_RECORD_TIME_INTERVAL',
  SET_IS_PAUSED: 'SET_IS_PAUSED',
  SET_SILENT_COUNT: 'SET_SILENT_COUNT',
  SET_TRANSCRIPT: 'SET_TRANSCRIPT',
  SET_REALTIME_TRANSCRIPT: 'SET_REALTIME_TRANSCRIPT',
};

// Định nghĩa state tổng hợp
const initialState = {
  isStartMicrophone: false,
  audioBlob: null,
  isSpeaking: false,
  audioContext: null,
  analyser: null,
  stopByTimeout: false,
  isEnglish: true,
  forceUpdate: 0,
  random: 0,
  recordTime: 0,
  recordTimeInterval: null,
  isPaused: false,
  silentCount: 0,
  transcript: '',
  realtimeTranscript: '', // Transcript realtime cho UI
};

// Reducer function
function reducer(state, action) {
  switch (action.type) {
    case actionTypes.SET_IS_START_MICROPHONE:
      return {...state, isStartMicrophone: action.payload};
    case actionTypes.SET_AUDIO_BLOB:
      return {...state, audioBlob: action.payload};
    case actionTypes.SET_IS_SPEAKING:
      return {...state, isSpeaking: action.payload};
    case actionTypes.SET_AUDIO_CONTEXT:
      return {...state, audioContext: action.payload};
    case actionTypes.SET_ANALYSER:
      return {...state, analyser: action.payload};
    case actionTypes.SET_STOP_BY_TIMEOUT:
      return {...state, stopByTimeout: action.payload};
    case actionTypes.SET_IS_ENGLISH:
      return {...state, isEnglish: action.payload};
    case actionTypes.FORCE_UPDATE:
      return {...state, forceUpdate: state.forceUpdate + 1};
    case actionTypes.SET_RANDOM:
      return {...state, random: Math.random()};
    case actionTypes.SET_RECORD_TIME:
      return {...state, recordTime: action.payload};
    case actionTypes.SET_RECORD_TIME_INTERVAL:
      return {...state, recordTimeInterval: action.payload};
    case actionTypes.SET_IS_PAUSED:
      return {...state, isPaused: action.payload};
    case actionTypes.SET_SILENT_COUNT:
      return {...state, silentCount: action.payload};
    case actionTypes.SET_TRANSCRIPT:
      return {...state, transcript: action.payload};
    case actionTypes.SET_REALTIME_TRANSCRIPT:
      return {...state, realtimeTranscript: action.payload};
    default:
      return state;
  }
}

const useMicrophone = () => {
  const userMediaStreamRef = useRef<MediaStream | null>(null);
  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const stopBeepAudioRef = useRef<HTMLAudioElement | null>(null);
  const startBeepAudioRef = useRef<HTMLAudioElement | null>(null);
  const isCancelRef = useRef<boolean>(false);
  const [state, dispatch] = useReducer(reducer, initialState);
  const recognitionRef = useRef<any>(null);
  const transcriptRef = useRef<string>(''); // Ref để lưu transcript mới nhất
  const accumulatedTranscriptRef = useRef<string>(''); // Ref để tích lũy toàn bộ transcript
  const lastProcessedIndexRef = useRef<number>(0); // Track index cuối đã xử lý

  // New refs for debounce and race condition prevention
  const lastActionTimeRef = useRef<number>(0);
  const isStartingRef = useRef<boolean>(false);
  const isStoppingRef = useRef<boolean>(false);
  const recordingStartTimeRef = useRef<number>(0);

  // New ref for isPaused
  const isPausedRef = useRef(false);

  // Helper
  const setIsRecording = (val) => dispatch({type: actionTypes.SET_IS_START_MICROPHONE, payload: val});
  const setAudioBlob = (val) => dispatch({type: actionTypes.SET_AUDIO_BLOB, payload: val});
  const setIsSpeaking = (val) => dispatch({type: actionTypes.SET_IS_SPEAKING, payload: val});
  const setAudioContext = (val) => dispatch({type: actionTypes.SET_AUDIO_CONTEXT, payload: val});
  const setAnalyser = (val) => dispatch({type: actionTypes.SET_ANALYSER, payload: val});
  const setStopByTimeout = (val) => dispatch({type: actionTypes.SET_STOP_BY_TIMEOUT, payload: val});
  const setIsEnglish = (val) => dispatch({type: actionTypes.SET_IS_ENGLISH, payload: val});
  const setRandom = () => dispatch({type: actionTypes.SET_RANDOM});
  const setRecordTime = (val) => dispatch({type: actionTypes.SET_RECORD_TIME, payload: val});
  const setRecordTimeInterval = (val) => dispatch({type: actionTypes.SET_RECORD_TIME_INTERVAL, payload: val});
  const setIsPaused = (val) => {
    isPausedRef.current = val;
    dispatch({type: actionTypes.SET_IS_PAUSED, payload: val});
  };
  const setSilentCount = (val) => dispatch({type: actionTypes.SET_SILENT_COUNT, payload: val});
  const setTranscript = useCallback((val) => {
    transcriptRef.current = val; // Cập nhật ref ngay lập tức
    accumulatedTranscriptRef.current = val; // Cập nhật accumulated transcript
    dispatch({type: actionTypes.SET_TRANSCRIPT, payload: val});
  }, []);

  const setRealtimeTranscript = useCallback((val) => {
    dispatch({type: actionTypes.SET_REALTIME_TRANSCRIPT, payload: val});
  }, []);

  // Utility functions for debounce and validation
  const canPerformStartAction = useCallback(() => {
    const now = Date.now();
    const timeSinceLastAction = now - lastActionTimeRef.current;

    if (isStartingRef.current) {
      console.warn('Start operation already in progress, skipping...');
      return false;
    }

    if (timeSinceLastAction < DEBOUNCE_DELAY) {
      console.warn(`Action too soon, need to wait ${DEBOUNCE_DELAY - timeSinceLastAction}ms more`);
      return false;
    }

    return true;
  }, []);

  const canPerformStopAction = useCallback(() => {
    const now = Date.now();
    const timeSinceLastAction = now - lastActionTimeRef.current;

    if (isStoppingRef.current) {
      console.warn('Stop operation already in progress, skipping...');
      return false;
    }

    // Allow stop even if start is in progress, but with debounce
    if (timeSinceLastAction < DEBOUNCE_DELAY) {
      console.warn(`Action too soon, need to wait ${DEBOUNCE_DELAY - timeSinceLastAction}ms more`);
      return false;
    }

    return true;
  }, []);

  const cleanupResources = useCallback(() => {
    // Stop speech recognition
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
        console.log('Speech recognition stopped in cleanup');
      } catch (error) {
        console.warn('Error stopping speech recognition in cleanup:', error);
      }
    }

    // Stop media recorder
    if (mediaRecorder.current && mediaRecorder.current.state !== 'inactive') {
      try {
        mediaRecorder.current.stop();
      } catch (error) {
        console.warn('Error stopping media recorder:', error);
      }
    }

    // Release microphone
    if (userMediaStreamRef.current) {
      userMediaStreamRef.current.getTracks().forEach((track) => track.stop());
      userMediaStreamRef.current = null;
    }

    // Close audio context
    if (state.audioContext && state.audioContext.state !== 'closed') {
      try {
        state.audioContext.close();
      } catch (error) {
        console.warn('Error closing audio context:', error);
      }
      setAudioContext(null);
    }

    setAnalyser(null);
    isStartingRef.current = false;
    isStoppingRef.current = false;
  }, [state.audioContext]);

  const validateAudioData = useCallback((audioBlob: Blob): boolean => {
    if (!audioBlob || audioBlob.size === 0) {
      console.warn('Invalid audio blob: empty or null');
      return false;
    }

    if (audioBlob.size < 1000) { // Less than 1KB is likely invalid
      console.warn('Audio blob too small, likely invalid:', audioBlob.size);
      return false;
    }

    return true;
  }, []);

  const startMicrophone = async (step = 0) => {
    console.log('step', step);
    console.log('start recording ...', Date.now());

    // Check if we can perform this action (debounce + state validation)
    if (!canPerformStartAction()) {
      return;
    }

    // Check if already recording
    if (state.isStartMicrophone || (mediaRecorder.current && mediaRecorder.current.state === 'recording')) {
      console.warn('Already recording, ignoring start request');
      return;
    }

    // Set operation flag and update last action time
    isStartingRef.current = true;
    lastActionTimeRef.current = Date.now();
    recordingStartTimeRef.current = Date.now();

    try {
      // Cleanup any existing resources before starting
      cleanupResources();

      // Ensure cancel flag is reset for new recording
      isCancelRef.current = false;
      console.log('🎤 Cancel flag reset to false');

      // Set recording state early to prevent timing issues
      setIsRecording(true);
      console.log('🎤 Recording state set to true early');

      setStopByTimeout(false);
      if (userMediaStreamRef.current) {
        userMediaStreamRef.current.getAudioTracks().forEach((track) => {
          track.enabled = false;
        });
      }
      if (startBeepAudioRef.current) {
        await startBeepAudioRef.current.play();
      }
      if (userMediaStreamRef.current) {
        userMediaStreamRef.current.getAudioTracks().forEach((track) => {
          track.enabled = true;
        });
      }
      setAudioBlob(null); // Reset audioBlob at the start of recording
      setSilentCount(0); // Reset silentCount
      setRecordTime(0); // Reset record time
      setIsPaused(false); // Reset isPaused
      setIsEnglish(true); // Reset language detection
      setTranscript(''); // Reset transcript
      setRealtimeTranscript(''); // Reset realtime transcript
      transcriptRef.current = ''; // Reset transcript ref
      accumulatedTranscriptRef.current = ''; // Reset accumulated transcript
      lastProcessedIndexRef.current = 0; // Reset processed index

      // Initialize speech recognition
      recognitionRef.current = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
      recognitionRef.current.continuous = true;
      recognitionRef.current.lang = 'en-US';
      recognitionRef.current.interimResults = true;

      recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
        if (isPausedRef.current) return;
        let newFinalTranscript = '';
        let interimTranscript = '';

        // Xử lý tất cả results, chỉ lấy những cái mới (từ lastProcessedIndex trở đi)
        for (let i = lastProcessedIndexRef.current; i < event.results.length; i++) {
          const result = event.results[i];
          const transcript = result[0].transcript;

          if (result.isFinal) {
            newFinalTranscript += transcript;
            lastProcessedIndexRef.current = i + 1; // Cập nhật index đã xử lý
          } else {
            interimTranscript += transcript;
          }
        }

        // Tạo realtime transcript (kết hợp accumulated + interim)
        const currentAccumulated = accumulatedTranscriptRef.current;
        let realtimeText = '';

        if (newFinalTranscript.trim()) {
          // Có final transcript mới
          const updatedTranscript = currentAccumulated
            ? `${currentAccumulated} ${newFinalTranscript.trim()}`
            : newFinalTranscript.trim();

          setTranscript(updatedTranscript);
          realtimeText = updatedTranscript;
          console.log('New final transcript added:', newFinalTranscript);
          console.log('Full accumulated transcript:', updatedTranscript);
        } else if (interimTranscript.trim()) {
          // Chỉ có interim transcript
          const previewTranscript = currentAccumulated
            ? `${currentAccumulated} ${interimTranscript.trim()}`
            : interimTranscript.trim();

          // Cập nhật transcriptRef để preview nhưng không cập nhật accumulated
          transcriptRef.current = previewTranscript;
          dispatch({type: actionTypes.SET_TRANSCRIPT, payload: previewTranscript});
          realtimeText = previewTranscript;
        } else {
          // Không có transcript mới, sử dụng accumulated hiện tại
          realtimeText = currentAccumulated;
        }

        // Cập nhật realtime transcript cho UI
        setRealtimeTranscript(realtimeText);
      };

      recognitionRef.current.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        // Release microphone and stop recording when error occurs
        cleanupResources();
        setIsPaused(false);
        if (typeof state.recordTimeInterval === 'number') {
          cancelAnimationFrame(state.recordTimeInterval);
        }
        setRecordTimeInterval(null);
        setIsRecording(false);
      };

      recognitionRef.current.onstart = () => {
        console.log('Speech recognition started');
        // Reset index khi recognition restart
        lastProcessedIndexRef.current = 0;
      };

      recognitionRef.current.onend = () => {
        console.log('Speech recognition ended');
        // Có thể restart nếu cần thiết
      };

      recognitionRef.current.start();
      setRandom();
      await initMicrophone();

      // Reset starting flag after successful initialization
      isStartingRef.current = false;
      console.log('✅ Start process completed - isStartingRef reset to false');

      const startTime = performance.now();
      let previousTime = startTime;
      const updateRecordTime = () => {
        const currentTime = performance.now();
        if (!state.isPaused) {
          setRecordTime(state.recordTime + (currentTime - previousTime) / 1000);
        }
        previousTime = currentTime;
        setRecordTime(Math.floor((currentTime - startTime) / 1000));
        setRecordTimeInterval(requestAnimationFrame(updateRecordTime));
      };
      if (!state.recordTimeInterval) {
        setRecordTimeInterval(requestAnimationFrame(updateRecordTime));
      }
    } catch (error) {
      console.error('Error starting microphone:', error);
      cleanupResources();
      setIsRecording(false);
    } finally {
      isStartingRef.current = false;
    }
  };

  const stopMicrophone = async (isStopByTimeOut = false) => {
    console.log('🛑 Stop microphone requested...', Date.now());
    if (!canPerformStopAction()) {
      return;
    }

    // Check if not currently recording - prioritize MediaRecorder state over React state
    const isMediaRecorderActive = mediaRecorder.current &&
      (mediaRecorder.current.state === 'recording' || mediaRecorder.current.state === 'paused');

    if (!state.isStartMicrophone && !isMediaRecorderActive) {
      console.warn('Not currently recording, ignoring stop request');
      return;
    }

    // Allow stop if either React state says recording OR MediaRecorder is active
    if (!state.isStartMicrophone && isMediaRecorderActive) {
      console.log('🔄 React state not updated yet, but MediaRecorder is active - allowing stop');
    }

    // Check minimum recording duration (unless stopped by timeout)
    if (!isStopByTimeOut && recordingStartTimeRef.current > 0) {
      const recordingDuration = Date.now() - recordingStartTimeRef.current;
      if (recordingDuration < MIN_RECORDING_DURATION) {
        console.warn(`Recording too short (${recordingDuration}ms), minimum is ${MIN_RECORDING_DURATION}ms`);
        return;
      }
    }

    // Set operation flag and update last action time
    isStoppingRef.current = true;
    lastActionTimeRef.current = Date.now();

    try {
      console.log('🛑 Starting stop process...');

      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
          recognitionRef.current = null; // Đặt về null để tránh xử lý thêm
        } catch (error) {
          console.warn('Error stopping speech recognition:', error);
          recognitionRef.current = null; // Vẫn đặt về null ngay cả khi có lỗi
        }
      }

      if (isStopByTimeOut) {
        setStopByTimeout(true);
      }

      if (mediaRecorder.current) {
        mediaRecorder.current.stop();
      }

      setIsPaused(false); // Reset isPaused
      if (typeof state.recordTimeInterval === 'number') {
        cancelAnimationFrame(state.recordTimeInterval);
      } // Cancel the recordTime animation frame
      setRecordTimeInterval(null); // Reset the interval reference
      setIsRecording(false);
      releaseMicrophone();
      setRandom();
      if (stopBeepAudioRef.current) {
        await stopBeepAudioRef.current.play();
      }
      await delay(100); // Adjust the delay as needed

      console.log('✅ Stop process completed successfully');

    } catch (error) {
      console.error('Error stopping microphone:', error);
      cleanupResources();
    } finally {
      isStoppingRef.current = false;
      recordingStartTimeRef.current = 0;
    }
  };

  const checkLanguage = useCallback(async (): Promise<boolean> => {
    try {
      // Sử dụng accumulated transcript để có toàn bộ nội dung
      const text = accumulatedTranscriptRef.current || transcriptRef.current || state.transcript;

      if (!text || text.trim() === '') {
        return true; // Default to English if no transcript
      }

      // Use franc to detect language
      const detectedLang = franc(text, {minLength: 3});
      return detectedLang !== 'vi' && detectedLang !== 'vie';
    } catch (error) {
      console.error('Language detection error:', error);
      return true; // Default to English on error
    }
  }, [state.transcript]);

  const initMicrophone = async () => {
    try {
      userMediaStreamRef.current = await navigator.mediaDevices.getUserMedia({audio: true});

      if (userMediaStreamRef.current) {
        // @ts-ignore
        mediaRecorder.current = new MediaRecorder(userMediaStreamRef.current);

        let chunks: Blob[] = [];

        mediaRecorder.current.ondataavailable = (event) => {
          if (event.data.size > 0) {
            chunks.push(event.data);
          }
        };

        mediaRecorder.current.onstop = async () => {
          try {
            console.log('🎵 MediaRecorder.onstop triggered, isCancelRef:', isCancelRef.current);

            if (isCancelRef.current) {
              console.log('❌ Recording was cancelled, skipping audio processing');
              isCancelRef.current = false;
              chunks = [];
              return;
            }

            console.log('✅ Recording completed normally, processing audio...');

            const blob = new Blob(chunks, {type: 'audio/wav'});
            console.log('Audio blob created, size:', blob.size);

            // Validate audio blob before processing
            if (!validateAudioData(blob)) {
              console.warn('Invalid audio blob, skipping processing');
              chunks = [];
              return;
            }

            // Delay để đảm bảo transcript được cập nhật từ speech recognition
            await delay(500);

            // Check language before setting audioBlob
            const isEnglishSpeech = await checkLanguage();
            setIsEnglish(isEnglishSpeech);

            // Convert and set audio blob only if it's English
            if (isEnglishSpeech) {
              try {
                const convertedBlob = await convertSampleRate(blob, 16000);
                setAudioBlob(convertedBlob);
              } catch (conversionError) {
                console.error('Error converting sample rate:', conversionError);
                console.warn('Using original blob due to conversion error');
                setAudioBlob(blob);
              }
            } else {
              setAudioBlob(blob);
            }

            chunks = []; // Clear the chunks for the next recording
          } catch (error) {
            console.error('Error in mediaRecorder.onstop:', error);
            chunks = [];
          }
        };

        mediaRecorder.current.start();
        console.log('🎤 MediaRecorder started, state:', mediaRecorder.current.state);
      }
    } catch (err) {
      console.error('Error accessing the microphone:', err);
    }
  };

  const releaseMicrophone = () => {
    if (userMediaStreamRef.current) {
      userMediaStreamRef.current.getTracks().forEach((track) => track.stop());
      userMediaStreamRef.current = null;
    }
    if (state.audioContext && state.audioContext.state !== 'closed') {
      state.audioContext.close();
      setAudioContext(null);
    }
    setAnalyser(null);
  };
  const convertSampleRate = async (audioBlob: Blob, targetSampleRate: number) => {
    try {
      // Validate audio data first
      if (!validateAudioData(audioBlob)) {
        console.warn('Invalid audio data, returning original blob');
        return audioBlob;
      }

      const arrayBuffer = await audioBlob.arrayBuffer();

      // Additional validation for array buffer
      if (!arrayBuffer || arrayBuffer.byteLength === 0) {
        console.warn('Empty array buffer, returning original blob');
        return audioBlob;
      }

      const audioCtx = new AudioContext();
      let audioBuffer: AudioBuffer;

      try {
        audioBuffer = await audioCtx.decodeAudioData(arrayBuffer);
      } catch (decodeError) {
        console.error('Failed to decode audio data:', decodeError);
        console.warn('Returning original blob due to decode error');

        // Cleanup audio context
        if (audioCtx.state !== 'closed') {
          await audioCtx.close();
        }

        return audioBlob; // Return original blob as fallback
      }

      // Validate decoded audio buffer
      if (!audioBuffer || audioBuffer.length === 0 || audioBuffer.duration === 0) {
        console.warn('Invalid decoded audio buffer, returning original blob');
        if (audioCtx.state !== 'closed') {
          await audioCtx.close();
        }
        return audioBlob;
      }

      const offlineCtx = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.duration * targetSampleRate,
        targetSampleRate
      );

      const bufferSource = offlineCtx.createBufferSource();
      bufferSource.buffer = audioBuffer;

      bufferSource.connect(offlineCtx.destination);
      bufferSource.start();

      const renderedBuffer = await offlineCtx.startRendering();

      // Cleanup audio context
      if (audioCtx.state !== 'closed') {
        await audioCtx.close();
      }

      return audioBufferToWav(renderedBuffer);
    } catch (error) {
      console.error('Error in convertSampleRate:', error);
      console.warn('Returning original blob due to conversion error');
      return audioBlob; // Return original blob as fallback
    }
  };

  const audioBufferToWav = (buffer: AudioBuffer) => {
    const numOfChan = buffer.numberOfChannels,
      length = buffer.length * numOfChan * 2 + 44,
      bufferArray = new ArrayBuffer(length),
      view = new DataView(bufferArray),
      channels = [];
    let offset = 0;
    let sample = 0;
    let pos = 0;

    setUint32(0x46464952); // "RIFF"
    setUint32(length - 8); // file length - 8
    setUint32(0x45564157); // "WAVE"

    setUint32(0x20746d66); // "fmt " chunk
    setUint32(16); // length = 16
    setUint16(1); // PCM (uncompressed)
    setUint16(numOfChan);
    setUint32(buffer.sampleRate);
    setUint32(buffer.sampleRate * 2 * numOfChan); // avg. bytes/sec
    setUint16(numOfChan * 2); // block-align
    setUint16(16); // 16-bit (hardcoded in this demo)

    setUint32(0x61746164); // "data" - chunk
    setUint32(length - pos - 4); // chunk length

    for (let i = 0; i < buffer.numberOfChannels; i++)
      // @ts-ignore
      channels.push(buffer.getChannelData(i));

    while (pos < length) {
      for (let i = 0; i < numOfChan; i++) {
        sample = Math.max(-1, Math.min(1, channels[i][offset])); // clamp
        sample = (0.5 + sample < 0 ? sample * 32768 : sample * 32767) | 0; // scale to 16-bit signed int
        view.setInt16(pos, sample, true); // write 16-bit sample
        pos += 2;
      }
      offset++; // next source sample
    }

    return new Blob([bufferArray], {type: 'audio/wav'});

    function setUint16(data) {
      view.setUint16(pos, data, true);
      pos += 2;
    }

    function setUint32(data) {
      view.setUint32(pos, data, true);
      pos += 4;
    }
  };

  useEffect(() => {
    // Create audio element for beep sound
    const startAudioElement = new Audio('https://file.langenter.com/config/sound/start.mp3');
    startAudioElement.preload = 'auto';
    startAudioElement.volume = 1; // Adjust volume as needed
    startBeepAudioRef.current = startAudioElement;
    // Create audio element for beep sound
    const stopAudioElement = new Audio('https://file.langenter.com/config/sound/stop.mp3');
    stopAudioElement.preload = 'auto';
    stopAudioElement.volume = 1; // Adjust volume as needed
    stopBeepAudioRef.current = stopAudioElement;
  }, []);

  useEffect(() => {
    const createAudioNodes = () => {
      // @ts-ignore
      const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
      const analyserNode = audioCtx.createAnalyser();
      analyserNode.fftSize = 512;
      // @ts-ignore
      const mediaStreamSource = audioCtx.createMediaStreamSource(userMediaStreamRef.current);
      mediaStreamSource.connect(analyserNode);

      setAudioContext(audioCtx);
      setAnalyser(analyserNode);
    };

    if (userMediaStreamRef.current && !state.audioContext) {
      createAudioNodes();
    }
  }, [userMediaStreamRef.current, state.audioContext]); // Depend on userMediaStreamRef.current

  useEffect(() => {
    if (state.isStartMicrophone && state.audioContext && state.analyser) {
      const checkSpeakingInterval = setInterval(() => {
        checkSpeaking();
      }, 100); // Adjust the interval as needed

      return () => {
        clearInterval(checkSpeakingInterval);
      };
    }
  }, [state.isStartMicrophone, state.audioContext, state.analyser]);
  const checkSpeaking = () => {
    if (state.isPaused) return;
    const volumeThreshold = VOLUME_THRESHOLD; // Set the volume threshold to 100
    const bufferLength = state.analyser?.frequencyBinCount || 0;
    const dataArray = new Uint8Array(bufferLength);

    state.analyser?.getByteFrequencyData(dataArray);
    const average = dataArray.reduce((acc, val) => acc + val, 0) / bufferLength;

    const speaking = average > volumeThreshold;
    //console.log('Average Volume:', average);
    setIsSpeaking(speaking);

    // If volume is below the threshold, increment silentCount; otherwise, reset it
    if (!speaking) {
      setSilentCount(state.silentCount + 1);
    } else {
      setSilentCount(0);
    }
    // If silentCount reaches 30 (3 seconds with a 100ms interval), stop recording
    if (state.silentCount >= TIMEOUT_SPEAKING * 10) {
      stopMicrophone(true);
      if (state.recordTime <= TIMEOUT_SPEAKING) setRecordTime(TIMEOUT_SPEAKING);
      setSilentCount(0); // Reset silentCount after stopping recording
    }
  };

  const pauseMicrophone = async () => {
    if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
      // Lưu transcript hiện tại vào accumulatedTranscriptRef trước khi pause
      const currentTranscript = transcriptRef.current || state.transcript;
      if (currentTranscript && currentTranscript.trim()) {
        accumulatedTranscriptRef.current = currentTranscript;
        console.log('Saved transcript before pause:', currentTranscript);
      }

      // Dừng speech recognition để không thu thập âm thanh mới
      if (recognitionRef.current) {
        try {
          recognitionRef.current.stop();
          console.log('Speech recognition stopped during pause');
        } catch (error) {
          console.warn('Error stopping speech recognition during pause:', error);
        }
      }

      mediaRecorder.current.pause();
      setIsPaused(true); // Update the ref
      console.log('Paused recording at', Date.now());
      await new Promise((resolve) => setTimeout(resolve, 100));
      return true;
    }
    return false;
  };

  const resumeMicrophone = async () => {
    if (mediaRecorder.current && mediaRecorder.current.state === 'paused') {
      // Khởi động lại speech recognition khi resume
      try {
        recognitionRef.current = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
        recognitionRef.current.continuous = true;
        recognitionRef.current.lang = 'en-US';
        recognitionRef.current.interimResults = true;

        recognitionRef.current.onresult = (event: SpeechRecognitionEvent) => {
          if (isPausedRef.current) return;
          let newFinalTranscript = '';
          let interimTranscript = '';

          // Xử lý tất cả results, chỉ lấy những cái mới (từ lastProcessedIndex trở đi)
          for (let i = lastProcessedIndexRef.current; i < event.results.length; i++) {
            const result = event.results[i];
            const transcript = result[0].transcript;

            if (result.isFinal) {
              newFinalTranscript += transcript;
              lastProcessedIndexRef.current = i + 1; // Cập nhật index đã xử lý
            } else {
              interimTranscript += transcript;
            }
          }

          // Tạo realtime transcript (kết hợp accumulated + interim)
          const currentAccumulated = accumulatedTranscriptRef.current;
          let realtimeText = '';

          if (newFinalTranscript.trim()) {
            // Có final transcript mới
            const updatedTranscript = currentAccumulated
              ? `${currentAccumulated} ${newFinalTranscript.trim()}`
              : newFinalTranscript.trim();

            setTranscript(updatedTranscript);
            realtimeText = updatedTranscript;
            console.log('New final transcript added after resume:', newFinalTranscript);
            console.log('Full accumulated transcript:', updatedTranscript);
          } else if (interimTranscript.trim()) {
            // Chỉ có interim transcript
            const previewTranscript = currentAccumulated
              ? `${currentAccumulated} ${interimTranscript.trim()}`
              : interimTranscript.trim();

            // Cập nhật transcriptRef để preview nhưng không cập nhật accumulated
            transcriptRef.current = previewTranscript;
            dispatch({type: actionTypes.SET_TRANSCRIPT, payload: previewTranscript});
            realtimeText = previewTranscript;
          } else {
            // Không có transcript mới, sử dụng accumulated hiện tại
            realtimeText = currentAccumulated;
          }

          // Cập nhật realtime transcript cho UI
          setRealtimeTranscript(realtimeText);
        };

        recognitionRef.current.onerror = (event: any) => {
          console.error('Speech recognition error after resume:', event.error);
          // Release microphone and stop recording when error occurs
          cleanupResources();
          setIsPaused(false);
          if (typeof state.recordTimeInterval === 'number') {
            cancelAnimationFrame(state.recordTimeInterval);
          }
          setRecordTimeInterval(null);
          setIsRecording(false);
        };

        recognitionRef.current.onstart = () => {
          console.log('Speech recognition restarted after resume');
          // Reset index khi recognition restart vì đây là session mới
          lastProcessedIndexRef.current = 0;
        };

        recognitionRef.current.onend = () => {
          console.log('Speech recognition ended after resume');
        };

        recognitionRef.current.start();
        console.log('Speech recognition restarted during resume');

        // Khôi phục transcript từ accumulatedTranscriptRef ngay lập tức
        const savedTranscript = accumulatedTranscriptRef.current;
        if (savedTranscript) {
          transcriptRef.current = savedTranscript;
          setTranscript(savedTranscript);
          setRealtimeTranscript(savedTranscript);
          console.log('Restored transcript after resume:', savedTranscript);
        }
      } catch (error) {
        console.error('Error restarting speech recognition during resume:', error);
      }

      mediaRecorder.current.resume();
      setIsPaused(false); // Update the ref
      await new Promise((resolve) => setTimeout(resolve, 100));
      console.log('Resumed recording at', Date.now());
      return true;
    }
    return false;
  };
  const cancelRecording = () => {
    console.log('Cancelling recording...');

    // Set cancel flag
    isCancelRef.current = true;

    // Use comprehensive cleanup
    cleanupResources();

    // Reset states
    setAudioBlob(null);
    setIsRecording(false);
    setIsPaused(false);
    setRandom();

    // Reset operation flags
    isStartingRef.current = false;
    isStoppingRef.current = false;
    recordingStartTimeRef.current = 0;

    delay(100);
  };
  useEffect(() => {
    console.log('🚀 useMicrophone hook initialized');

    // Initialize states without calling cancelRecording to avoid setting isCancelRef.current = true
    setAudioBlob(null);
    setIsRecording(false);
    setIsPaused(false);

    // Reset operation flags
    isStartingRef.current = false;
    isStoppingRef.current = false;
    recordingStartTimeRef.current = 0;

    // Ensure isCancelRef is false for first-time use
    isCancelRef.current = false;

    console.log('✅ Initial state reset completed');

    // Cleanup khi component unmounts
    return () => {
      console.log('🧹 Cleaning up useMicrophone hook');
      cancelRecording();
    };
  }, []);

  const downloadAudio = () => {
    if (state.audioBlob) {
      const url = URL.createObjectURL(state.audioBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'recording.wav'; // Tên file với đuôi .wav
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
    }
  };

  // Function để lấy transcript mới nhất (ưu tiên accumulated transcript)
  const getCurrentTranscript = useCallback(() => {
    return accumulatedTranscriptRef.current || transcriptRef.current || state.transcript;
  }, [state.transcript]);

  return {
    ...state,
    startMicrophone,
    stopMicrophone,
    pauseMicrophone,
    resumeMicrophone,
    cancelRecording,
    downloadAudio,
    setAudioBlob,
    getCurrentTranscript, // Export function để lấy transcript mới nhất
  };
};

export default useMicrophone;
